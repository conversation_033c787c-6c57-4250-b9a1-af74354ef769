import 'dart:async';
import 'package:flutter_tts/flutter_tts.dart';
import '../core/config/app_config.dart';
import '../core/logging/app_logger.dart';

/// Énumération des erreurs TTS
enum TTSError {
  notInitialized,
  emptyText,
  platformError,
  networkError,
  configurationError,
  alreadySpeaking,
}

/// Résultat d'une opération TTS
class TTSResult {
  final bool success;
  final TTSError? error;
  final String? errorMessage;
  
  TTSResult.success() : success = true, error = null, errorMessage = null;
  TTSResult.error(this.error, this.errorMessage) : success = false;
  
  @override
  String toString() => success 
      ? 'TTSResult.success' 
      : 'TTSResult.error($error: $errorMessage)';
}

/// Service de synthèse vocale amélioré avec gestion d'erreurs robuste
class TextToSpeechService {
  static final TextToSpeechService _instance = TextToSpeechService._internal();
  factory TextToSpeechService() => _instance;
  TextToSpeechService._internal();

  final logger = AppLogger.getLogger('TextToSpeechService');
  final FlutterTts _flutterTts = FlutterTts();
  bool _isInitialized = false;
  bool _isSpeaking = false;
  
  // StreamController pour diffuser l'état de synthèse
  StreamController<bool>? _speakingController;
  Stream<bool> get speakingStream => _speakingController?.stream ?? const Stream.empty();
  
  // Queue pour gérer les demandes multiples
  final List<String> _speechQueue = [];
  bool _isProcessingQueue = false;
  
  // Getters publics
  bool get isInitialized => _isInitialized;
  bool get isSpeaking => _isSpeaking;
  int get queueLength => _speechQueue.length;

  /// Initialise le service Text-to-Speech avec configuration robuste
  Future<TTSResult> initialize() async {
    if (_isInitialized) {
      return TTSResult.success();
    }

    try {
      logger.info('🔊 Initialisation du service TTS...');
      
      // Initialiser le contrôleur de stream
      _speakingController = StreamController<bool>.broadcast();

      // Configuration avec les paramètres de AppConfig
      await _flutterTts.setLanguage(AppConfig.ttsLanguage);
      await _flutterTts.setSpeechRate(AppConfig.ttsSpeechRate);
      await _flutterTts.setVolume(AppConfig.ttsVolume);
      await _flutterTts.setPitch(AppConfig.ttsPitch);

      // Configuration des callbacks avec logging
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        _speakingController?.add(true);
        logger.info('🔊 Début de la synthèse vocale');
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        _speakingController?.add(false);
        logger.info('🔊 Fin de la synthèse vocale');
        _processNextInQueue();
      });

      _flutterTts.setErrorHandler((msg) {
        _isSpeaking = false;
        _speakingController?.add(false);
        logger.severe('🔊 Erreur TTS: $msg');
        _processNextInQueue();
      });

      _isInitialized = true;
      logger.info('🔊 Service TTS initialisé avec succès');
      
      return TTSResult.success();
    } catch (e) {
      final error = 'Erreur initialisation TTS: $e';
      logger.severe(error, e);
      return TTSResult.error(TTSError.configurationError, error);
    }
  }

  /// Synthèse vocale avec gestion d'erreurs complète
  Future<TTSResult> speak(String text) async {
    // Validation des paramètres
    if (!_isInitialized) {
      const error = 'Service TTS non initialisé';
      logger.warning(error);
      return TTSResult.error(TTSError.notInitialized, error);
    }

    if (text.trim().isEmpty) {
      const error = 'Texte vide fourni pour la synthèse';
      logger.warning(error);
      return TTSResult.error(TTSError.emptyText, error);
    }

    try {
      // Si déjà en cours de synthèse, ajouter à la queue
      if (_isSpeaking) {
        _speechQueue.add(text);
        logger.info('🔊 Texte ajouté à la queue: "${_truncateText(text)}"');
        return TTSResult.success();
      }

      // Synthèse immédiate
      logger.info('🔊 Synthèse vocale: "${_truncateText(text)}"');
      final result = await _flutterTts.speak(text);
      
      if (result == 1) {
        LogUtils.logTTS(text, true);
        return TTSResult.success();
      } else {
        const error = 'Échec de la synthèse vocale (code de retour invalide)';
        LogUtils.logTTS(text, false, error: error);
        return TTSResult.error(TTSError.platformError, error);
      }
    } catch (e) {
      final error = 'Erreur lors de la synthèse: $e';
      logger.severe(error, e);
      LogUtils.logTTS(text, false, error: error);
      return TTSResult.error(TTSError.platformError, error);
    }
  }

  /// Synthèse vocale avec feedback contextuel pour les commandes
  Future<TTSResult> speakCommandFeedback(String command) async {
    final message = _getCommandFeedbackMessage(command);
    return await speak(message);
  }

  /// Arrête la synthèse en cours
  Future<TTSResult> stop() async {
    if (!_isInitialized) {
      return TTSResult.error(TTSError.notInitialized, 'Service non initialisé');
    }

    try {
      await _flutterTts.stop();
      _speechQueue.clear();
      _isSpeaking = false;
      _speakingController?.add(false);
      logger.info('🔊 Synthèse vocale arrêtée');
      return TTSResult.success();
    } catch (e) {
      final error = 'Erreur lors de l\'arrêt: $e';
      logger.severe(error, e);
      return TTSResult.error(TTSError.platformError, error);
    }
  }

  /// Traite le prochain élément de la queue
  void _processNextInQueue() {
    if (_speechQueue.isNotEmpty && !_isProcessingQueue) {
      _isProcessingQueue = true;
      final nextText = _speechQueue.removeAt(0);
      
      // Délai court pour éviter les conflits
      Future.delayed(const Duration(milliseconds: 100), () {
        _isProcessingQueue = false;
        speak(nextText);
      });
    }
  }

  /// Obtient le message de feedback pour une commande
  String _getCommandFeedbackMessage(String command) {
    switch (command) {
      case 'accueil':
        return 'Navigation vers l\'accueil';
      case 'sos_alerte':
        return 'Alerte SOS déclenchée';
      case 'appels':
        return 'Accès aux appels';
      case 'messages':
        return 'Accès aux messages';
      case 'position':
        return 'Affichage de votre position';
      case 'time':
        final now = DateTime.now();
        return 'Il est ${now.hour} heures ${now.minute.toString().padLeft(2, '0')}';
      case 'canne':
        return 'Connexion de la canne intelligente';
      default:
        if (command.startsWith('unknown:')) {
          final unknownCommand = command.substring(8);
          return 'Commande "$unknownCommand" non reconnue. Dites "aide" pour la liste des commandes.';
        }
        return 'Action exécutée';
    }
  }

  /// Tronque le texte pour les logs
  String _truncateText(String text, {int maxLength = 50}) {
    return text.length <= maxLength 
        ? text 
        : '${text.substring(0, maxLength)}...';
  }

  /// Libère les ressources
  void dispose() {
    logger.info('🔊 Fermeture du service TTS');
    _speechQueue.clear();
    _speakingController?.close();
    _speakingController = null;
    _isInitialized = false;
    _isSpeaking = false;
  }
}
