import 'dart:async';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../main.dart' as main;

/// Service de gestion de la caméra pour les alertes SOS
class CameraService {
  static CameraService? _instance;
  static CameraService get instance => _instance ??= CameraService._();
  CameraService._();

  CameraController? _controller;
  List<CameraDescription> _cameras = [];
  bool _isInitialized = false;
  bool _isRecording = false;
  String? _currentVideoPath;
  Timer? _recordingTimer;
  int _recordingDuration = 0;

  /// Initialise les caméras disponibles
  Future<void> initialize() async {
    try {
      _cameras = main.cameras;
      _isInitialized = _cameras.isNotEmpty;

      if (kDebugMode) {
        print('CameraService: ${_cameras.length} caméras trouvées');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur initialisation caméras: $e');
      }
      _isInitialized = false;
    }
  }

  /// Démarre la caméra avec la caméra arrière par défaut
  Future<CameraController?> startCamera({bool useBackCamera = true}) async {
    if (!_isInitialized || _cameras.isEmpty) {
      await initialize();
    }

    if (_cameras.isEmpty) {
      throw Exception('Aucune caméra disponible');
    }

    // Sélectionner la caméra (arrière par défaut, sinon la première disponible)
    CameraDescription selectedCamera;
    if (useBackCamera) {
      selectedCamera = _cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => _cameras.first,
      );
    } else {
      selectedCamera = _cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => _cameras.first,
      );
    }

    try {
      _controller = CameraController(
        selectedCamera,
        ResolutionPreset.medium,
        enableAudio: true,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller!.initialize();
      
      if (kDebugMode) {
        print('CameraService: Caméra initialisée (${selectedCamera.lensDirection})');
      }

      return _controller;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur démarrage caméra: $e');
      }
      _controller?.dispose();
      _controller = null;
      rethrow;
    }
  }

  /// Démarre l'enregistrement vidéo
  Future<String?> startRecording() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw Exception('Caméra non initialisée');
    }

    if (_isRecording) {
      if (kDebugMode) {
        print('CameraService: Enregistrement déjà en cours');
      }
      return _currentVideoPath;
    }

    try {
      // Créer le répertoire de stockage
      final directory = await getApplicationDocumentsDirectory();
      final videoDir = Directory(path.join(directory.path, 'sos_videos'));
      if (!await videoDir.exists()) {
        await videoDir.create(recursive: true);
      }

      // Générer un nom de fichier unique
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'sos_video_$timestamp.mp4';
      _currentVideoPath = path.join(videoDir.path, fileName);

      // Démarrer l'enregistrement
      await _controller!.startVideoRecording();
      _isRecording = true;
      _recordingDuration = 0;

      // Démarrer le timer pour compter la durée
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _recordingDuration++;
      });

      if (kDebugMode) {
        print('CameraService: Enregistrement démarré - $_currentVideoPath');
      }

      return _currentVideoPath;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur démarrage enregistrement: $e');
      }
      _isRecording = false;
      _currentVideoPath = null;
      rethrow;
    }
  }

  /// Arrête l'enregistrement vidéo
  Future<String?> stopRecording() async {
    if (!_isRecording || _controller == null) {
      return null;
    }

    try {
      final videoFile = await _controller!.stopVideoRecording();
      _isRecording = false;
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // Déplacer le fichier vers le chemin souhaité si nécessaire
      if (_currentVideoPath != null && videoFile.path != _currentVideoPath) {
        final targetFile = File(_currentVideoPath!);
        final sourceFile = File(videoFile.path);
        await sourceFile.copy(targetFile.path);
        await sourceFile.delete();
      }

      if (kDebugMode) {
        print('CameraService: Enregistrement arrêté - Durée: ${_recordingDuration}s');
        print('CameraService: Fichier sauvé: $_currentVideoPath');
      }

      final savedPath = _currentVideoPath;
      _currentVideoPath = null;
      _recordingDuration = 0;

      return savedPath;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur arrêt enregistrement: $e');
      }
      _isRecording = false;
      _recordingTimer?.cancel();
      _recordingTimer = null;
      _currentVideoPath = null;
      rethrow;
    }
  }

  /// Prend une photo
  Future<String?> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw Exception('Caméra non initialisée');
    }

    try {
      // Créer le répertoire de stockage
      final directory = await getApplicationDocumentsDirectory();
      final photoDir = Directory(path.join(directory.path, 'sos_photos'));
      if (!await photoDir.exists()) {
        await photoDir.create(recursive: true);
      }

      // Prendre la photo
      final image = await _controller!.takePicture();
      
      // Générer un nom de fichier unique
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'sos_photo_$timestamp.jpg';
      final targetPath = path.join(photoDir.path, fileName);

      // Déplacer la photo vers le répertoire cible
      final targetFile = File(targetPath);
      await image.saveTo(targetFile.path);

      if (kDebugMode) {
        print('CameraService: Photo prise - $targetPath');
      }

      return targetPath;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur prise de photo: $e');
      }
      rethrow;
    }
  }

  /// Arrête la caméra et libère les ressources
  Future<void> stopCamera() async {
    try {
      if (_isRecording) {
        await stopRecording();
      }

      _recordingTimer?.cancel();
      _recordingTimer = null;
      
      await _controller?.dispose();
      _controller = null;
      
      if (kDebugMode) {
        print('CameraService: Caméra arrêtée');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur arrêt caméra: $e');
      }
    }
  }

  /// Change de caméra (avant/arrière)
  Future<void> switchCamera() async {
    if (_cameras.length < 2) {
      throw Exception('Une seule caméra disponible');
    }

    final currentLensDirection = _controller?.description.lensDirection;
    final useBackCamera = currentLensDirection == CameraLensDirection.front;

    await stopCamera();
    await startCamera(useBackCamera: useBackCamera);
  }

  /// Getters pour l'état du service
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  int get recordingDuration => _recordingDuration;
  CameraController? get controller => _controller;
  List<CameraDescription> get availableCameras => _cameras;
  bool get hasMultipleCameras => _cameras.length > 1;

  /// Nettoie les anciens fichiers (plus de 7 jours)
  Future<void> cleanupOldFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final sosDir = Directory(path.join(directory.path, 'sos_videos'));
      final photoDir = Directory(path.join(directory.path, 'sos_photos'));

      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));

      // Nettoyer les vidéos
      if (await sosDir.exists()) {
        await for (final file in sosDir.list()) {
          if (file is File) {
            final stat = await file.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await file.delete();
              if (kDebugMode) {
                print('CameraService: Fichier supprimé - ${file.path}');
              }
            }
          }
        }
      }

      // Nettoyer les photos
      if (await photoDir.exists()) {
        await for (final file in photoDir.list()) {
          if (file is File) {
            final stat = await file.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await file.delete();
              if (kDebugMode) {
                print('CameraService: Photo supprimée - ${file.path}');
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur nettoyage fichiers: $e');
      }
    }
  }
}
