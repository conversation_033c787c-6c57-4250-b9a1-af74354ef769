import 'dart:convert';
import 'package:canne_connectee/shared/models/message.dart';
import 'package:canne_connectee/shared/services/contact_service.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class MessageService {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  static const String _messagesKey = 'messages';
  static const String _conversationsKey = 'conversations';
  
  final ContactService _contactService = ContactService();
  
  List<Message> _messages = [];
  Map<String, Conversation> _conversations = {};
  bool _isInitialized = false;

  List<Message> get messages => List.unmodifiable(_messages);
  List<Conversation> get conversations => _conversations.values.toList()
    ..sort((a, b) => b.lastActivity.compareTo(a.lastActivity));

  /// Initialise le service de messages
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadMessages();
      await _loadConversations();
      
      // Ajouter des messages par défaut si aucun message n'existe
      if (_messages.isEmpty) {
        await _addDefaultMessages();
      }
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('MessageService: Initialisé avec ${_messages.length} messages');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur initialisation: $e');
      }
    }
  }

  /// Charge les messages depuis le stockage local
  Future<void> _loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesKey);
      
      if (messagesJson != null) {
        final List<dynamic> messagesList = json.decode(messagesJson);
        _messages = messagesList.map((json) => Message.fromJson(json)).toList();
        
        // Trier par date
        _messages.sort((a, b) => a.sentAt.compareTo(b.sentAt));
        
        if (kDebugMode) {
          print('MessageService: ${_messages.length} messages chargés');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur chargement messages: $e');
      }
    }
  }

  /// Charge les conversations depuis le stockage local
  Future<void> _loadConversations() async {
    try {
      // Reconstruire les conversations à partir des messages
      _conversations.clear();
      
      for (final message in _messages) {
        final contactId = message.contactId;
        
        if (!_conversations.containsKey(contactId)) {
          _conversations[contactId] = Conversation(
            contactId: contactId,
            messages: [],
            lastActivity: message.sentAt,
          );
        }
        
        _conversations[contactId]!.messages.add(message);
        
        // Mettre à jour la dernière activité
        if (message.sentAt.isAfter(_conversations[contactId]!.lastActivity)) {
          _conversations[contactId] = Conversation(
            contactId: contactId,
            messages: _conversations[contactId]!.messages,
            lastActivity: message.sentAt,
            unreadCount: _conversations[contactId]!.unreadCount,
          );
        }
      }
      
      // Calculer les messages non lus
      for (final contactId in _conversations.keys) {
        final unreadCount = _conversations[contactId]!.messages
            .where((m) => !m.isFromMe && !m.isRead)
            .length;
        
        _conversations[contactId] = Conversation(
          contactId: contactId,
          messages: _conversations[contactId]!.messages,
          lastActivity: _conversations[contactId]!.lastActivity,
          unreadCount: unreadCount,
        );
      }
      
      if (kDebugMode) {
        print('MessageService: ${_conversations.length} conversations chargées');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur chargement conversations: $e');
      }
    }
  }

  /// Sauvegarde les messages dans le stockage local
  Future<void> _saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = json.encode(_messages.map((m) => m.toJson()).toList());
      await prefs.setString(_messagesKey, messagesJson);
      
      if (kDebugMode) {
        print('MessageService: ${_messages.length} messages sauvegardés');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur sauvegarde messages: $e');
      }
    }
  }

  /// Ajoute des messages par défaut
  Future<void> _addDefaultMessages() async {
    final now = DateTime.now();
    
    final defaultMessages = [
      Message(
        id: 'msg_1',
        contactId: 'dr_martin',
        content: 'Votre rendez-vous est confirmé pour demain à 14h',
        type: MessageType.text,
        status: MessageStatus.read,
        isFromMe: false,
        sentAt: now.subtract(const Duration(hours: 2)),
        deliveredAt: now.subtract(const Duration(hours: 2)),
        readAt: now.subtract(const Duration(minutes: 30)),
      ),
      Message(
        id: 'msg_2',
        contactId: 'famille_kouassi',
        content: 'Comment allez-vous aujourd\'hui ?',
        type: MessageType.text,
        status: MessageStatus.delivered,
        isFromMe: false,
        sentAt: now.subtract(const Duration(days: 1)),
        deliveredAt: now.subtract(const Duration(days: 1)),
      ),
      Message(
        id: 'msg_3',
        contactId: 'centre_medical',
        content: 'Rappel: Prise de médicament à 18h',
        type: MessageType.text,
        status: MessageStatus.read,
        isFromMe: false,
        sentAt: now.subtract(const Duration(days: 1, hours: 6)),
        deliveredAt: now.subtract(const Duration(days: 1, hours: 6)),
        readAt: now.subtract(const Duration(days: 1, hours: 5)),
      ),
      Message(
        id: 'msg_4',
        contactId: 'pharmacie_coin',
        content: 'Vos médicaments sont prêts',
        type: MessageType.text,
        status: MessageStatus.delivered,
        isFromMe: false,
        sentAt: now.subtract(const Duration(days: 3)),
        deliveredAt: now.subtract(const Duration(days: 3)),
      ),
    ];

    for (final message in defaultMessages) {
      await sendMessage(message);
    }
  }

  /// Envoie un message
  Future<Message> sendMessage(Message message) async {
    final newMessage = message.copyWith(
      id: message.id.isEmpty ? _generateId() : message.id,
      sentAt: message.sentAt,
      status: MessageStatus.sending,
    );
    
    _messages.add(newMessage);
    await _saveMessages();
    await _loadConversations();
    
    // Simuler l'envoi
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mettre à jour le statut
    final sentMessage = newMessage.copyWith(
      status: MessageStatus.sent,
      deliveredAt: DateTime.now(),
    );
    
    final index = _messages.indexWhere((m) => m.id == newMessage.id);
    if (index != -1) {
      _messages[index] = sentMessage;
      await _saveMessages();
      await _loadConversations();
    }
    
    // Mettre à jour la date du dernier message du contact
    await _contactService.updateLastMessage(newMessage.contactId);
    
    if (kDebugMode) {
      print('MessageService: Message envoyé: ${newMessage.content}');
    }
    
    return sentMessage;
  }

  /// Envoie un SMS via l'application native
  Future<bool> sendSMS(String phoneNumber, String message) async {
    try {
      final uri = Uri(
        scheme: 'sms',
        path: phoneNumber,
        queryParameters: {'body': message},
      );
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur envoi SMS: $e');
      }
      return false;
    }
  }

  /// Envoie un message d'urgence
  Future<bool> sendEmergencyMessage(String contactId, String message) async {
    try {
      final contact = _contactService.getContactById(contactId);
      if (contact == null) return false;
      
      // Envoyer via l'app de messages
      final emergencyMessage = Message(
        id: '',
        contactId: contactId,
        content: '🚨 URGENCE: $message',
        type: MessageType.emergency,
        status: MessageStatus.sending,
        isFromMe: true,
        sentAt: DateTime.now(),
        isEmergency: true,
      );
      
      await sendMessage(emergencyMessage);
      
      // Envoyer aussi via SMS natif
      await sendSMS(contact.phone, '🚨 URGENCE: $message');
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('MessageService: Erreur message d\'urgence: $e');
      }
      return false;
    }
  }

  /// Obtient les messages d'une conversation
  List<Message> getMessagesForContact(String contactId) {
    return _messages.where((m) => m.contactId == contactId).toList()
      ..sort((a, b) => a.sentAt.compareTo(b.sentAt));
  }

  /// Marque les messages comme lus
  Future<void> markMessagesAsRead(String contactId) async {
    bool hasChanges = false;
    
    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      if (message.contactId == contactId && !message.isFromMe && !message.isRead) {
        _messages[i] = message.copyWith(
          readAt: DateTime.now(),
          status: MessageStatus.read,
        );
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      await _saveMessages();
      await _loadConversations();
    }
  }

  /// Supprime un message
  Future<bool> deleteMessage(String messageId) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index == -1) return false;
    
    _messages.removeAt(index);
    await _saveMessages();
    await _loadConversations();
    
    return true;
  }

  /// Supprime une conversation
  Future<bool> deleteConversation(String contactId) async {
    _messages.removeWhere((m) => m.contactId == contactId);
    _conversations.remove(contactId);
    
    await _saveMessages();
    
    return true;
  }

  /// Génère un ID unique
  String _generateId() {
    return 'msg_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Nettoie les ressources
  Future<void> dispose() async {
    _messages.clear();
    _conversations.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('MessageService: Ressources libérées');
    }
  }
}
