import 'package:flutter/material.dart';
import 'package:canne_connectee/features/communication/pages/appels.dart';
import 'package:canne_connectee/features/sos/pages/sos.dart';
import 'package:canne_connectee/features/communication/pages/messages.dart';
import 'package:canne_connectee/features/location/pages/historique.dart';
import 'package:canne_connectee/features/auth/pages/user_type_selection_page.dart';
import 'package:canne_connectee/features/communication/pages/proches_page.dart';
import 'package:canne_connectee/features/hardware/pages/canne_connection_page.dart';
import 'package:canne_connectee/features/location/pages/real_time_location_page.dart';
import 'package:canne_connectee/features/hardware/pages/notifications_page.dart';
import 'features/voice/services/voice_command_service.dart';
import 'features/auth/services/auth_service.dart';
import 'core/services/notification_service.dart';
import 'features/voice/services/text_to_speech_service.dart';
import 'shared/widgets/home_content_widget.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool isListening = false;
  bool isSpeaking = false;

  late AnimationController _controller;
  late Animation<double> _animation;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  final VoiceCommandService _voiceCommandService = VoiceCommandService();
  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();
  final TextToSpeechService _ttsService = TextToSpeechService();

  final List<Widget> _pages = [
    const HomeContentWidget(),

    const SOSPage(),
    const AppelsPage(),
    const MessagesPage(),
    const HistoriquePage(),
  ];

  @override
  void initState() {
    super.initState();

    // Animation pour le scale du bouton
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Animation pour l'effet wave
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeOut),
    );

    // Initialiser les services
    _initializeVoiceCommands();
    _notificationService.initialize();
    _initializeTTS();
  }

  Future<void> _initializeVoiceCommands() async {
    await _voiceCommandService.initialize();

    // Écouter les commandes vocales
    _voiceCommandService.commandStream.listen((command) {
      _handleVoiceCommand(command);
    });
  }

  Future<void> _initializeTTS() async {
    try {
      print('🔊 Initialisation du TTS...');
      await _ttsService.initialize();

      print('🔊 TTS initialisé avec succès');
      // Message de bienvenue
      Future.delayed(const Duration(seconds: 2), () {
        print('🔊 Test du TTS...');
        _ttsService.speak('Commandes vocales activées');
      });
    } catch (e) {
      print('❌ Échec de l\'initialisation du TTS: $e');
    }

    // Écouter l'état du TTS
    _ttsService.speakingStream.listen((speaking) {
      print('🔊 TTS speaking: $speaking');
      if (mounted) {
        setState(() {
          isSpeaking = speaking;
        });
      }
    });
  }

  Future<void> _toggleListening() async {
    setState(() {
      isListening = !isListening;
    });

    if (isListening) {
      // Démarrer les animations
      _controller.repeat(reverse: true);
      _waveController.repeat();

      // Démarrer l'écoute des commandes vocales
      await _voiceCommandService.startListening();

      // Arrêter automatiquement après 10 secondes
      Future.delayed(const Duration(seconds: 10), () {
        if (isListening) {
          _stopListening();
        }
      });
    } else {
      _stopListening();
    }
  }

  Future<void> _stopListening() async {
    setState(() {
      isListening = false;
    });

    // Arrêter les animations
    _controller.stop();
    _controller.reset();
    _waveController.stop();
    _waveController.reset();

    // Arrêter l'écoute des commandes vocales
    await _voiceCommandService.stopListening();
  }

  void _handleVoiceCommand(String command) {
    print('🎤 Commande vocale reçue: $command');

    // Arrêter l'écoute après avoir reçu une commande
    _stopListening();

    // Traiter les 7 commandes essentielles
    switch (command) {
      case 'accueil':
        setState(() {
          _selectedIndex = 0;
        });
        break;

      case 'sos_alerte':
        setState(() {
          _selectedIndex = 1; // Aller à la page SOS
        });
        _notificationService.addSOSNotification();
        break;

      case 'appels':
        setState(() {
          _selectedIndex = 2;
        });
        break;

      case 'messages':
        setState(() {
          _selectedIndex = 3;
        });
        break;

      case 'position':
        // Ouvrir la page de localisation
        Future.delayed(const Duration(milliseconds: 800), () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const RealTimeLocationPage(),
            ),
          );
        });
        break;

      case 'time':
        // Le message vocal est géré par le service TTS
        break;

      case 'canne':
        // Ouvrir la page de connexion canne
        Future.delayed(const Duration(milliseconds: 800), () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CanneConnectionPage(),
            ),
          );
        });
        break;

      default:
        if (command.startsWith('unknown:')) {
          command.substring(8);
          // Afficher l'aide après le message vocal
          Future.delayed(const Duration(milliseconds: 2000), () {
            _showAvailableCommands();
          });
        }
    }
  }

  // ignore: unused_element
  void _showCommandFeedback(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.mic, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFFFF7900),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showAvailableCommands() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.mic, color: Color(0xFFFF7900)),
            SizedBox(width: 8),
            Text(
              'Commandes Vocales',
              style: TextStyle(
                color: Color(0xFFFF7900),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Dites une de ces 7 commandes :',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              _buildCommandItem('🏠', 'Accueil', 'Dites "accueil" ou "home"'),
              _buildCommandItem('🚨', 'SOS', 'Dites "sos" ou "alerte"'),
              _buildCommandItem('📞', 'Appels', 'Dites "appels" ou "téléphone"'),
              _buildCommandItem('💬', 'Messages', 'Dites "messages" ou "sms"'),
              _buildCommandItem('📍', 'Position', 'Dites "position" ou "où suis-je"'),
              _buildCommandItem('🕐', 'Heure', 'Dites "heure" ou "quelle heure"'),
              _buildCommandItem('🔗', 'Canne', 'Dites "canne" ou "bluetooth"'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Compris'),
          ),
        ],
      ),
    );
  }

  Widget _buildCommandItem(String emoji, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Naviguer vers la page des proches
  void _navigateToProches() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProchesPage(),
      ),
    );
  }

  Future<void> _handleLogout() async {
    // Afficher une boîte de dialogue de confirmation
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Déconnexion',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(0xFFFF7900),
            ),
          ),
          content: const Text(
            'Êtes-vous sûr de vouloir vous déconnecter ?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Déconnexion'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      try {
        // Effectuer la déconnexion
        await _authService.logout();

        // Afficher un message de confirmation
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.logout, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text('Déconnexion réussie'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );

          // Naviguer vers la page de sélection du type d'utilisateur
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const UserTypeSelectionPage(),
            ),
            (route) => false,
          );
        }
      } catch (e) {
        // Afficher un message d'erreur
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(child: Text('Erreur lors de la déconnexion: $e')),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _waveController.dispose();
    _voiceCommandService.dispose();
    _ttsService.dispose();
    super.dispose();
  }

  Widget _buildAnimatedMicButton() {
    return SizedBox(
      width: 80,
      height: 80,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Effet wave (ondulations)
          if (isListening) ...[
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Container(
                  width: 80 + (_waveAnimation.value * 40),
                  height: 80 + (_waveAnimation.value * 40),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFF7900).withOpacity(0.3 - (_waveAnimation.value * 0.3)),
                      width: 2,
                    ),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                final delayedValue = (_waveAnimation.value - 0.3).clamp(0.0, 1.0);
                return Container(
                  width: 80 + (delayedValue * 60),
                  height: 80 + (delayedValue * 60),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFF7900).withOpacity(0.2 - (delayedValue * 0.2)),
                      width: 1.5,
                    ),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                final delayedValue = (_waveAnimation.value - 0.6).clamp(0.0, 1.0);
                return Container(
                  width: 80 + (delayedValue * 80),
                  height: 80 + (delayedValue * 80),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFF7900).withOpacity(0.1 - (delayedValue * 0.1)),
                      width: 1,
                    ),
                  ),
                );
              },
            ),
          ],

          // Bouton principal avec animation de scale
          ScaleTransition(
            scale: isListening ? _animation : const AlwaysStoppedAnimation(1.0),
            child: GestureDetector(
              onLongPress: _showAvailableCommands, // Appui long pour voir les commandes
              child: Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isListening
                        ? [Colors.red, Colors.red.shade700]
                        : [const Color(0xFFFF7900), const Color(0xFFE66A00)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (isListening ? Colors.red : const Color(0xFFFF7900)).withOpacity(0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(35),
                    onTap: _toggleListening,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        isListening ? Icons.mic : Icons.mic_none,
                        size: 35,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingButtonStack() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton contextuel selon la page active
        if (_getContextualButton() != null) ...[
          _getContextualButton()!,
          const SizedBox(height: 16),
        ],
        // Bouton Microphone toujours en bas
        _buildAnimatedMicButton(),

        // Indicateur d'aide ou de statut TTS
        if (!isListening)
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: isSpeaking
                  ? Colors.green.withOpacity(0.8)
                  : Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isSpeaking) ...[
                  const Icon(
                    Icons.volume_up,
                    color: Colors.white,
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                ],
                Flexible(
                  child: Text(
                    isSpeaking ? 'Réponse vocale...' : 'Appui long = Aide',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget? _getContextualButton() {
    switch (_selectedIndex) {
      case 2: // Page Contacts
        return _buildAddContactButton();
      case 3: // Page Messages
        return _buildNewMessageButton();
      default:
        return null; // Pas de bouton contextuel pour les autres pages
    }
  }

  Widget _buildAddContactButton() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2196F3).withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: () {
            // Afficher un message informatif pour ajouter un contact
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Utilisez l\'onglet "Contacts" pour ajouter un nouveau contact'),
                backgroundColor: Color(0xFF2196F3),
                duration: Duration(seconds: 3),
              ),
            );
          },
          child: const Center(
            child: Icon(
              Icons.person_add,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNewMessageButton() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: () {
            // Appeler la fonction pour créer un nouveau message
            _showNewMessageDialog();
          },
          child: const Center(
            child: Icon(
              Icons.edit,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  void _showNewMessageDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        String recipient = '';
        String messageText = '';

        return AlertDialog(
          title: const Text(
            'Nouveau message',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(0xFFFF7900),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Destinataire',
                  hintText: 'Nom ou numéro de téléphone',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                onChanged: (value) => recipient = value,
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Message',
                  hintText: 'Tapez votre message...',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.message),
                ),
                maxLines: 3,
                onChanged: (value) => messageText = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (recipient.isNotEmpty && messageText.isNotEmpty) {
                  // TODO: Implémenter l'envoi du message
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Message envoyé avec succès !'),
                      backgroundColor: Color(0xFF4CAF50),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Veuillez remplir tous les champs'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Envoyer'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: Colors.black, size: 28),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CanneConnectionPage(),
                ),
              );
            },
            icon: const Icon(Icons.bluetooth, size: 28, color: Colors.black),
            tooltip: 'Connexion Canne',
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RealTimeLocationPage(),
                ),
              );
            },
            icon: const Icon(Icons.location_on, size: 28, color: Colors.black),
            tooltip: 'Ma Position',
          ),
          ListenableBuilder(
            listenable: _notificationService,
            builder: (context, child) {
              return Stack(
                children: [
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationsPage(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.notifications, size: 28, color: Colors.black),
                    tooltip: 'Notifications',
                  ),
                  // Badge de notification
                  if (_notificationService.unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          _notificationService.unreadCount > 99
                              ? '99+'
                              : _notificationService.unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(color: Color(0xFFFF7900)),
              child: Text(
                'Menu',
                style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profil'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Paramètres'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Aide'),
              onTap: () => Navigator.pop(context),
            ),
            // Option "Mes Proches" disponible pour tous les utilisateurs
            ListTile(
              leading: const Icon(Icons.people, color: Color(0xFFFF7900)),
              title: const Text(
                'Mes Proches',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              onTap: () {
                Navigator.pop(context);
                _navigateToProches();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text(
                'Déconnexion',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
              ),
              onTap: () {
                Navigator.pop(context); // Fermer le drawer
                _handleLogout(); // Gérer la déconnexion
              },
            ),
          ],
        ),
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: const Color(0xFFEEEEEE),
        selectedItemColor: const Color(0xFFFF7900),
        unselectedItemColor: Colors.black54,
        currentIndex: _selectedIndex,
        onTap: (int index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home, size: 30), label: 'Accueil'),
          BottomNavigationBarItem(icon: Icon(Icons.warning, size: 30), label: 'Urgence'),
          BottomNavigationBarItem(icon: Icon(Icons.phone, size: 30), label: 'Appels'),
          BottomNavigationBarItem(icon: Icon(Icons.message, size: 30), label: 'Messages'),
          BottomNavigationBarItem(icon: Icon(Icons.history, size: 30), label: 'Historique'),
        ],
      ),
      floatingActionButtonAnimator: FloatingActionButtonAnimator.scaling,
      floatingActionButton: _buildFloatingButtonStack(),
    );
  }
}
