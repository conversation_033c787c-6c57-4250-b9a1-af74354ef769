enum RelationType {
  famille,
  ami,
  aidant,
  professionnel,
}

extension RelationTypeExtension on RelationType {
  String get displayName {
    switch (this) {
      case RelationType.famille:
        return 'Famille';
      case RelationType.ami:
        return 'Ami(e)';
      case RelationType.aidant:
        return 'Aidant';
      case RelationType.professionnel:
        return 'Professionnel';
    }
  }

  String get icon {
    switch (this) {
      case RelationType.famille:
        return '👨‍👩‍👧‍👦';
      case RelationType.ami:
        return '👥';
      case RelationType.aidant:
        return '🤝';
      case RelationType.professionnel:
        return '👨‍⚕️';
    }
  }
}

enum ProchePriority {
  urgence,
  haute,
  normale,
  faible,
}

extension ProchePriorityExtension on ProchePriority {
  String get displayName {
    switch (this) {
      case ProchePriority.urgence:
        return 'Urgence';
      case ProchePriority.haute:
        return 'Haute';
      case ProchePriority.normale:
        return 'Normale';
      case ProchePriority.faible:
        return 'Faible';
    }
  }

  String get color {
    switch (this) {
      case ProchePriority.urgence:
        return '#FF0000';
      case ProchePriority.haute:
        return '#FF7900';
      case ProchePriority.normale:
        return '#2196F3';
      case ProchePriority.faible:
        return '#4CAF50';
    }
  }
}

class Proche {
  final String id;
  final String userId; // ID de l'utilisateur malvoyant
  final String nom;
  final String prenom;
  final String email;
  final String? telephone;
  final RelationType relationType;
  final ProchePriority priority;
  final bool canReceiveAlerts;
  final bool canTrackLocation;
  final bool canReceiveCalls;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? invitationToken;
  final bool isInvitationAccepted;
  final DateTime? invitationSentAt;
  final DateTime? invitationAcceptedAt;

  Proche({
    required this.id,
    required this.userId,
    required this.nom,
    required this.prenom,
    required this.email,
    this.telephone,
    required this.relationType,
    this.priority = ProchePriority.normale,
    this.canReceiveAlerts = true,
    this.canTrackLocation = true,
    this.canReceiveCalls = true,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.invitationToken,
    this.isInvitationAccepted = false,
    this.invitationSentAt,
    this.invitationAcceptedAt,
  });

  factory Proche.fromJson(Map<String, dynamic> json) {
    return Proche(
      id: json['id']?.toString() ?? '',
      userId: json['user_id']?.toString() ?? '',
      nom: json['nom'] ?? '',
      prenom: json['prenom'] ?? '',
      email: json['email'] ?? '',
      telephone: json['telephone'],
      relationType: RelationType.values.firstWhere(
        (e) => e.toString() == 'RelationType.${json['relation_type']}',
        orElse: () => RelationType.famille,
      ),
      priority: ProchePriority.values.firstWhere(
        (e) => e.toString() == 'ProchePriority.${json['priority']}',
        orElse: () => ProchePriority.normale,
      ),
      canReceiveAlerts: json['can_receive_alerts'] ?? true,
      canTrackLocation: json['can_track_location'] ?? true,
      canReceiveCalls: json['can_receive_calls'] ?? true,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      invitationToken: json['invitation_token'],
      isInvitationAccepted: json['is_invitation_accepted'] ?? false,
      invitationSentAt: json['invitation_sent_at'] != null 
          ? DateTime.parse(json['invitation_sent_at']) 
          : null,
      invitationAcceptedAt: json['invitation_accepted_at'] != null 
          ? DateTime.parse(json['invitation_accepted_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'relation_type': relationType.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'can_receive_alerts': canReceiveAlerts,
      'can_track_location': canTrackLocation,
      'can_receive_calls': canReceiveCalls,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'invitation_token': invitationToken,
      'is_invitation_accepted': isInvitationAccepted,
      'invitation_sent_at': invitationSentAt?.toIso8601String(),
      'invitation_accepted_at': invitationAcceptedAt?.toIso8601String(),
    };
  }

  Proche copyWith({
    String? id,
    String? userId,
    String? nom,
    String? prenom,
    String? email,
    String? telephone,
    RelationType? relationType,
    ProchePriority? priority,
    bool? canReceiveAlerts,
    bool? canTrackLocation,
    bool? canReceiveCalls,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? invitationToken,
    bool? isInvitationAccepted,
    DateTime? invitationSentAt,
    DateTime? invitationAcceptedAt,
  }) {
    return Proche(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      relationType: relationType ?? this.relationType,
      priority: priority ?? this.priority,
      canReceiveAlerts: canReceiveAlerts ?? this.canReceiveAlerts,
      canTrackLocation: canTrackLocation ?? this.canTrackLocation,
      canReceiveCalls: canReceiveCalls ?? this.canReceiveCalls,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      invitationToken: invitationToken ?? this.invitationToken,
      isInvitationAccepted: isInvitationAccepted ?? this.isInvitationAccepted,
      invitationSentAt: invitationSentAt ?? this.invitationSentAt,
      invitationAcceptedAt: invitationAcceptedAt ?? this.invitationAcceptedAt,
    );
  }

  String get fullName => '$prenom $nom';

  String get statusText {
    if (!isInvitationAccepted && invitationToken != null) {
      return 'Invitation envoyée';
    } else if (isInvitationAccepted) {
      return 'Connecté';
    } else {
      return 'Non invité';
    }
  }

  bool get isPending => !isInvitationAccepted && invitationToken != null;
  bool get isConnected => isInvitationAccepted && isActive;

  @override
  String toString() {
    return 'Proche{id: $id, fullName: $fullName, email: $email, relationType: $relationType, priority: $priority}';
  }
}
