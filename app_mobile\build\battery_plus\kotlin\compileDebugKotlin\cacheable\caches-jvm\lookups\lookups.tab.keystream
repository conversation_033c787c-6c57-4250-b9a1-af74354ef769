  SuppressLint android.annotation  BroadcastReceiver android.content  ContentResolver android.content  Context android.content  ContextWrapper android.content  Intent android.content  IntentFilter android.content  BatteryManager !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  convertBatteryStatus !android.content.BroadcastReceiver  publishBatteryStatus !android.content.BroadcastReceiver  BATTERY_SERVICE android.content.Context  
POWER_SERVICE android.content.Context  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getLET android.content.Context  getLet android.content.Context  getSystemService android.content.Context  let android.content.Context  registerReceiver android.content.Context  setContentResolver android.content.Context  unregisterReceiver android.content.Context  registerReceiver android.content.ContextWrapper  ACTION_BATTERY_CHANGED android.content.Intent  getIntExtra android.content.Intent  BatteryManager 
android.os  Build 
android.os  PowerManager 
android.os  BATTERY_PROPERTY_CAPACITY android.os.BatteryManager  BATTERY_PROPERTY_STATUS android.os.BatteryManager  BATTERY_STATUS_CHARGING android.os.BatteryManager  BATTERY_STATUS_DISCHARGING android.os.BatteryManager  BATTERY_STATUS_FULL android.os.BatteryManager  BATTERY_STATUS_NOT_CHARGING android.os.BatteryManager  BATTERY_STATUS_UNKNOWN android.os.BatteryManager  EXTRA_LEVEL android.os.BatteryManager  EXTRA_SCALE android.os.BatteryManager  EXTRA_STATUS android.os.BatteryManager  getIntProperty android.os.BatteryManager  MANUFACTURER android.os.Build  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  getISPowerSaveMode android.os.PowerManager  getIsPowerSaveMode android.os.PowerManager  isPowerSaveMode android.os.PowerManager  setPowerSaveMode android.os.PowerManager  Settings android.provider  System android.provider.Settings  getInt  android.provider.Settings.System  	getString  android.provider.Settings.System  RequiresApi androidx.annotation  
ContextCompat androidx.core.content  RECEIVER_NOT_EXPORTED #androidx.core.content.ContextCompat  registerReceiver #androidx.core.content.ContextCompat  Any !dev.fluttercommunity.plus.battery  BatteryManager !dev.fluttercommunity.plus.battery  BatteryPlusPlugin !dev.fluttercommunity.plus.battery  Boolean !dev.fluttercommunity.plus.battery  Build !dev.fluttercommunity.plus.battery  Context !dev.fluttercommunity.plus.battery  
ContextCompat !dev.fluttercommunity.plus.battery  ContextWrapper !dev.fluttercommunity.plus.battery  EventChannel !dev.fluttercommunity.plus.battery  Int !dev.fluttercommunity.plus.battery  Intent !dev.fluttercommunity.plus.battery  IntentFilter !dev.fluttercommunity.plus.battery  Locale !dev.fluttercommunity.plus.battery  
MethodChannel !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_HUAWEI_NAME !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_HUAWEI_VALUE !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_SAMSUNG_NAME !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_SAMSUNG_VALUE !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_XIAOMI_NAME !dev.fluttercommunity.plus.battery  POWER_SAVE_MODE_XIAOMI_VALUE !dev.fluttercommunity.plus.battery  RECEIVER_NOT_EXPORTED !dev.fluttercommunity.plus.battery  Settings !dev.fluttercommunity.plus.battery  String !dev.fluttercommunity.plus.battery  VERSION !dev.fluttercommunity.plus.battery  
VERSION_CODES !dev.fluttercommunity.plus.battery  convertBatteryStatus !dev.fluttercommunity.plus.battery  let !dev.fluttercommunity.plus.battery  	lowercase !dev.fluttercommunity.plus.battery  publishBatteryStatus !dev.fluttercommunity.plus.battery  Any 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  BatteryManager 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Boolean 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  BroadcastReceiver 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Build 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Context 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  
ContextCompat 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  ContextWrapper 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  EventChannel 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  	EventSink 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  FlutterPluginBinding 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Int 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Intent 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  IntentFilter 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Locale 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  
MethodCall 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  
MethodChannel 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_HUAWEI_NAME 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_HUAWEI_VALUE 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_SAMSUNG_NAME 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_SAMSUNG_VALUE 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_XIAOMI_NAME 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  POWER_SAVE_MODE_XIAOMI_VALUE 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  PowerManager 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  RECEIVER_NOT_EXPORTED 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  RequiresApi 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Settings 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  String 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  SuppressLint 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  VERSION 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  
VERSION_CODES 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  applicationContext 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  chargingStateChangeReceiver 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  checkPowerServiceSaveMode 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  convertBatteryStatus 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  !createChargingStateChangeReceiver 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  eventChannel 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getBatteryLevel 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getBatteryProperty 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getBatteryStatus 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getLET 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getLOWERCASE 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getLet 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  getLowercase 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  isHuaweiPowerSaveModeActive 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  isInPowerSaveMode 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  isSamsungPowerSaveModeActive 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  isXiaomiPowerSaveModeActive 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  let 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  	lowercase 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  
methodChannel 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  publishBatteryStatus 3dev.fluttercommunity.plus.battery.BatteryPlusPlugin  Any =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  BatteryManager =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Boolean =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  BroadcastReceiver =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Build =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Context =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  
ContextCompat =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  ContextWrapper =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  EventChannel =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  	EventSink =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  FlutterPluginBinding =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Int =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Intent =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  IntentFilter =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Locale =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  
MethodCall =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  
MethodChannel =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_HUAWEI_NAME =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_HUAWEI_VALUE =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_SAMSUNG_NAME =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_SAMSUNG_VALUE =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_XIAOMI_NAME =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  POWER_SAVE_MODE_XIAOMI_VALUE =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  PowerManager =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  RECEIVER_NOT_EXPORTED =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  RequiresApi =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  Settings =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  String =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  SuppressLint =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  VERSION =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  
VERSION_CODES =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  convertBatteryStatus =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  getLET =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  getLOWERCASE =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  getLet =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  getLowercase =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  let =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  	lowercase =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  publishBatteryStatus =dev.fluttercommunity.plus.battery.BatteryPlusPlugin.Companion  getCONVERTBatteryStatus hdev.fluttercommunity.plus.battery.BatteryPlusPlugin.createChargingStateChangeReceiver.<no name provided>  getConvertBatteryStatus hdev.fluttercommunity.plus.battery.BatteryPlusPlugin.createChargingStateChangeReceiver.<no name provided>  getPUBLISHBatteryStatus hdev.fluttercommunity.plus.battery.BatteryPlusPlugin.createChargingStateChangeReceiver.<no name provided>  getPublishBatteryStatus hdev.fluttercommunity.plus.battery.BatteryPlusPlugin.createChargingStateChangeReceiver.<no name provided>  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  BatteryManager 	java.lang  Build 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  ContextWrapper 	java.lang  EventChannel 	java.lang  Intent 	java.lang  IntentFilter 	java.lang  Locale 	java.lang  
MethodChannel 	java.lang  POWER_SAVE_MODE_HUAWEI_NAME 	java.lang  POWER_SAVE_MODE_HUAWEI_VALUE 	java.lang  POWER_SAVE_MODE_SAMSUNG_NAME 	java.lang  POWER_SAVE_MODE_SAMSUNG_VALUE 	java.lang  POWER_SAVE_MODE_XIAOMI_NAME 	java.lang  POWER_SAVE_MODE_XIAOMI_VALUE 	java.lang  RECEIVER_NOT_EXPORTED 	java.lang  Settings 	java.lang  VERSION 	java.lang  
VERSION_CODES 	java.lang  convertBatteryStatus 	java.lang  let 	java.lang  	lowercase 	java.lang  publishBatteryStatus 	java.lang  Locale 	java.util  
getDefault java.util.Locale  Any kotlin  BatteryManager kotlin  Boolean kotlin  Build kotlin  Context kotlin  
ContextCompat kotlin  ContextWrapper kotlin  EventChannel kotlin  	Function1 kotlin  Int kotlin  Intent kotlin  IntentFilter kotlin  Locale kotlin  
MethodChannel kotlin  Nothing kotlin  POWER_SAVE_MODE_HUAWEI_NAME kotlin  POWER_SAVE_MODE_HUAWEI_VALUE kotlin  POWER_SAVE_MODE_SAMSUNG_NAME kotlin  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin  POWER_SAVE_MODE_XIAOMI_NAME kotlin  POWER_SAVE_MODE_XIAOMI_VALUE kotlin  RECEIVER_NOT_EXPORTED kotlin  Settings kotlin  String kotlin  VERSION kotlin  
VERSION_CODES kotlin  convertBatteryStatus kotlin  let kotlin  	lowercase kotlin  publishBatteryStatus kotlin  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  BatteryManager kotlin.annotation  Build kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  ContextWrapper kotlin.annotation  EventChannel kotlin.annotation  Intent kotlin.annotation  IntentFilter kotlin.annotation  Locale kotlin.annotation  
MethodChannel kotlin.annotation  POWER_SAVE_MODE_HUAWEI_NAME kotlin.annotation  POWER_SAVE_MODE_HUAWEI_VALUE kotlin.annotation  POWER_SAVE_MODE_SAMSUNG_NAME kotlin.annotation  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin.annotation  POWER_SAVE_MODE_XIAOMI_NAME kotlin.annotation  POWER_SAVE_MODE_XIAOMI_VALUE kotlin.annotation  RECEIVER_NOT_EXPORTED kotlin.annotation  Settings kotlin.annotation  VERSION kotlin.annotation  
VERSION_CODES kotlin.annotation  convertBatteryStatus kotlin.annotation  let kotlin.annotation  	lowercase kotlin.annotation  publishBatteryStatus kotlin.annotation  BatteryManager kotlin.collections  Build kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  ContextWrapper kotlin.collections  EventChannel kotlin.collections  Intent kotlin.collections  IntentFilter kotlin.collections  Locale kotlin.collections  
MethodChannel kotlin.collections  POWER_SAVE_MODE_HUAWEI_NAME kotlin.collections  POWER_SAVE_MODE_HUAWEI_VALUE kotlin.collections  POWER_SAVE_MODE_SAMSUNG_NAME kotlin.collections  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin.collections  POWER_SAVE_MODE_XIAOMI_NAME kotlin.collections  POWER_SAVE_MODE_XIAOMI_VALUE kotlin.collections  RECEIVER_NOT_EXPORTED kotlin.collections  Settings kotlin.collections  VERSION kotlin.collections  
VERSION_CODES kotlin.collections  convertBatteryStatus kotlin.collections  let kotlin.collections  	lowercase kotlin.collections  publishBatteryStatus kotlin.collections  BatteryManager kotlin.comparisons  Build kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  ContextWrapper kotlin.comparisons  EventChannel kotlin.comparisons  Intent kotlin.comparisons  IntentFilter kotlin.comparisons  Locale kotlin.comparisons  
MethodChannel kotlin.comparisons  POWER_SAVE_MODE_HUAWEI_NAME kotlin.comparisons  POWER_SAVE_MODE_HUAWEI_VALUE kotlin.comparisons  POWER_SAVE_MODE_SAMSUNG_NAME kotlin.comparisons  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin.comparisons  POWER_SAVE_MODE_XIAOMI_NAME kotlin.comparisons  POWER_SAVE_MODE_XIAOMI_VALUE kotlin.comparisons  RECEIVER_NOT_EXPORTED kotlin.comparisons  Settings kotlin.comparisons  VERSION kotlin.comparisons  
VERSION_CODES kotlin.comparisons  convertBatteryStatus kotlin.comparisons  let kotlin.comparisons  	lowercase kotlin.comparisons  publishBatteryStatus kotlin.comparisons  BatteryManager 	kotlin.io  Build 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  ContextWrapper 	kotlin.io  EventChannel 	kotlin.io  Intent 	kotlin.io  IntentFilter 	kotlin.io  Locale 	kotlin.io  
MethodChannel 	kotlin.io  POWER_SAVE_MODE_HUAWEI_NAME 	kotlin.io  POWER_SAVE_MODE_HUAWEI_VALUE 	kotlin.io  POWER_SAVE_MODE_SAMSUNG_NAME 	kotlin.io  POWER_SAVE_MODE_SAMSUNG_VALUE 	kotlin.io  POWER_SAVE_MODE_XIAOMI_NAME 	kotlin.io  POWER_SAVE_MODE_XIAOMI_VALUE 	kotlin.io  RECEIVER_NOT_EXPORTED 	kotlin.io  Settings 	kotlin.io  VERSION 	kotlin.io  
VERSION_CODES 	kotlin.io  convertBatteryStatus 	kotlin.io  let 	kotlin.io  	lowercase 	kotlin.io  publishBatteryStatus 	kotlin.io  BatteryManager 
kotlin.jvm  Build 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  ContextWrapper 
kotlin.jvm  EventChannel 
kotlin.jvm  Intent 
kotlin.jvm  IntentFilter 
kotlin.jvm  Locale 
kotlin.jvm  
MethodChannel 
kotlin.jvm  POWER_SAVE_MODE_HUAWEI_NAME 
kotlin.jvm  POWER_SAVE_MODE_HUAWEI_VALUE 
kotlin.jvm  POWER_SAVE_MODE_SAMSUNG_NAME 
kotlin.jvm  POWER_SAVE_MODE_SAMSUNG_VALUE 
kotlin.jvm  POWER_SAVE_MODE_XIAOMI_NAME 
kotlin.jvm  POWER_SAVE_MODE_XIAOMI_VALUE 
kotlin.jvm  RECEIVER_NOT_EXPORTED 
kotlin.jvm  Settings 
kotlin.jvm  VERSION 
kotlin.jvm  
VERSION_CODES 
kotlin.jvm  convertBatteryStatus 
kotlin.jvm  let 
kotlin.jvm  	lowercase 
kotlin.jvm  publishBatteryStatus 
kotlin.jvm  BatteryManager 
kotlin.ranges  Build 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  ContextWrapper 
kotlin.ranges  EventChannel 
kotlin.ranges  Intent 
kotlin.ranges  IntentFilter 
kotlin.ranges  Locale 
kotlin.ranges  
MethodChannel 
kotlin.ranges  POWER_SAVE_MODE_HUAWEI_NAME 
kotlin.ranges  POWER_SAVE_MODE_HUAWEI_VALUE 
kotlin.ranges  POWER_SAVE_MODE_SAMSUNG_NAME 
kotlin.ranges  POWER_SAVE_MODE_SAMSUNG_VALUE 
kotlin.ranges  POWER_SAVE_MODE_XIAOMI_NAME 
kotlin.ranges  POWER_SAVE_MODE_XIAOMI_VALUE 
kotlin.ranges  RECEIVER_NOT_EXPORTED 
kotlin.ranges  Settings 
kotlin.ranges  VERSION 
kotlin.ranges  
VERSION_CODES 
kotlin.ranges  convertBatteryStatus 
kotlin.ranges  let 
kotlin.ranges  	lowercase 
kotlin.ranges  publishBatteryStatus 
kotlin.ranges  BatteryManager kotlin.sequences  Build kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  ContextWrapper kotlin.sequences  EventChannel kotlin.sequences  Intent kotlin.sequences  IntentFilter kotlin.sequences  Locale kotlin.sequences  
MethodChannel kotlin.sequences  POWER_SAVE_MODE_HUAWEI_NAME kotlin.sequences  POWER_SAVE_MODE_HUAWEI_VALUE kotlin.sequences  POWER_SAVE_MODE_SAMSUNG_NAME kotlin.sequences  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin.sequences  POWER_SAVE_MODE_XIAOMI_NAME kotlin.sequences  POWER_SAVE_MODE_XIAOMI_VALUE kotlin.sequences  RECEIVER_NOT_EXPORTED kotlin.sequences  Settings kotlin.sequences  VERSION kotlin.sequences  
VERSION_CODES kotlin.sequences  convertBatteryStatus kotlin.sequences  let kotlin.sequences  	lowercase kotlin.sequences  publishBatteryStatus kotlin.sequences  BatteryManager kotlin.text  Build kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  ContextWrapper kotlin.text  EventChannel kotlin.text  Intent kotlin.text  IntentFilter kotlin.text  Locale kotlin.text  
MethodChannel kotlin.text  POWER_SAVE_MODE_HUAWEI_NAME kotlin.text  POWER_SAVE_MODE_HUAWEI_VALUE kotlin.text  POWER_SAVE_MODE_SAMSUNG_NAME kotlin.text  POWER_SAVE_MODE_SAMSUNG_VALUE kotlin.text  POWER_SAVE_MODE_XIAOMI_NAME kotlin.text  POWER_SAVE_MODE_XIAOMI_VALUE kotlin.text  RECEIVER_NOT_EXPORTED kotlin.text  Settings kotlin.text  VERSION kotlin.text  
VERSION_CODES kotlin.text  convertBatteryStatus kotlin.text  let kotlin.text  	lowercase kotlin.text  publishBatteryStatus kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     