import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'voice_service.dart';
import 'text_to_speech_service.dart';
import '../core/logging/app_logger.dart';

class VoiceCommandService {
  static final VoiceCommandService _instance = VoiceCommandService._internal();
  factory VoiceCommandService() => _instance;
  VoiceCommandService._internal();

  final logger = AppLogger.getLogger('VoiceCommandService');
  final SpeechToText _speechToText = SpeechToText();
  final VoiceService _voiceService = VoiceService();
  final TextToSpeechService _ttsService = TextToSpeechService();
  
  bool _isInitialized = false;
  bool _isListening = false;
  String _lastCommand = '';
  
  StreamController<String>? _commandController;
  StreamController<bool>? _listeningController;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  String get lastCommand => _lastCommand;

  // Streams
  Stream<String> get commandStream => _commandController?.stream ?? const Stream.empty();
  Stream<bool> get listeningStream => _listeningController?.stream ?? const Stream.empty();

  // 7 Commandes vocales essentielles
  final Map<String, VoiceCommand> _commands = {
    // 1. Navigation vers l'accueil
    'accueil': VoiceCommand('accueil', 'Aller à l\'accueil', VoiceCommandType.navigation),
    'home': VoiceCommand('accueil', 'Aller à l\'accueil', VoiceCommandType.navigation),
    'maison': VoiceCommand('accueil', 'Aller à l\'accueil', VoiceCommandType.navigation),

    // 2. Alerte SOS
    'sos': VoiceCommand('sos_alerte', 'Déclencher une alerte SOS', VoiceCommandType.action),
    'alerte': VoiceCommand('sos_alerte', 'Déclencher une alerte SOS', VoiceCommandType.action),
    'aide': VoiceCommand('sos_alerte', 'Déclencher une alerte SOS', VoiceCommandType.action),
    'secours': VoiceCommand('sos_alerte', 'Déclencher une alerte SOS', VoiceCommandType.action),
    'urgence': VoiceCommand('sos_alerte', 'Déclencher une alerte SOS', VoiceCommandType.action),

    // 3. Appels
    'appels': VoiceCommand('appels', 'Aller aux appels', VoiceCommandType.navigation),
    'téléphone': VoiceCommand('appels', 'Aller aux appels', VoiceCommandType.navigation),
    'appeler': VoiceCommand('appels', 'Aller aux appels', VoiceCommandType.navigation),

    // 4. Messages
    'messages': VoiceCommand('messages', 'Aller aux messages', VoiceCommandType.navigation),
    'sms': VoiceCommand('messages', 'Aller aux messages', VoiceCommandType.navigation),
    'texto': VoiceCommand('messages', 'Aller aux messages', VoiceCommandType.navigation),

    // 5. Position/Localisation
    'position': VoiceCommand('position', 'Obtenir ma position', VoiceCommandType.info),
    'localisation': VoiceCommand('position', 'Obtenir ma position', VoiceCommandType.info),
    'où suis-je': VoiceCommand('position', 'Obtenir ma position', VoiceCommandType.info),
    'carte': VoiceCommand('position', 'Obtenir ma position', VoiceCommandType.info),

    // 6. Heure
    'heure': VoiceCommand('time', 'Obtenir l\'heure', VoiceCommandType.info),
    'quelle heure': VoiceCommand('time', 'Obtenir l\'heure', VoiceCommandType.info),
    'temps': VoiceCommand('time', 'Obtenir l\'heure', VoiceCommandType.info),

    // 7. Connexion canne
    'canne': VoiceCommand('canne', 'Connexion de la canne', VoiceCommandType.action),
    'connecter canne': VoiceCommand('canne', 'Connexion de la canne', VoiceCommandType.action),
    'bluetooth': VoiceCommand('canne', 'Connexion de la canne', VoiceCommandType.action),
  };

  /// Initialise le service de commandes vocales
  Future<bool> initialize() async {
    try {
      // Initialiser le service vocal de base
      if (!_voiceService.isInitialized) {
        final voiceInitialized = await _voiceService.initialize();
        if (!voiceInitialized) {
          if (kDebugMode) {
            print('VoiceCommandService: Impossible d\'initialiser le service vocal');
          }
          return false;
        }
      }

      // Initialiser le service Text-to-Speech
      if (!_ttsService.isInitialized) {
        try {
          await _ttsService.initialize();
        } catch (e) {
          if (kDebugMode) {
            print('VoiceCommandService: Impossible d\'initialiser le TTS: $e');
          }
          // Continuer même si TTS échoue, ce n'est pas critique
        }
      }

      // Initialiser Speech-to-Text pour les commandes
      final speechAvailable = await _speechToText.initialize(
        onError: (error) {
          if (kDebugMode) {
            print('VoiceCommandService: Erreur Speech-to-Text: $error');
          }
        },
        onStatus: (status) {
          if (kDebugMode) {
            print('VoiceCommandService: Statut: $status');
          }
        },
      );

      if (!speechAvailable) {
        if (kDebugMode) {
          print('VoiceCommandService: Speech-to-Text non disponible');
        }
        return false;
      }

      // Initialiser les contrôleurs de stream
      _commandController = StreamController<String>.broadcast();
      _listeningController = StreamController<bool>.broadcast();

      _isInitialized = true;
      
      if (kDebugMode) {
        print('VoiceCommandService: Initialisé avec succès');
        print('VoiceCommandService: ${_commands.length} commandes disponibles');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('VoiceCommandService: Erreur initialisation: $e');
      }
      return false;
    }
  }

  /// Démarre l'écoute des commandes vocales
  Future<bool> startListening() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('VoiceCommandService: Service non initialisé');
      }
      return false;
    }

    if (_isListening) {
      if (kDebugMode) {
        print('VoiceCommandService: Déjà en écoute');
      }
      return true;
    }

    try {
      await _speechToText.listen(
        onResult: (result) {
          final command = result.recognizedWords.toLowerCase().trim();
          _lastCommand = command;

          if (kDebugMode) {
            print('VoiceCommandService: Commande reçue: "$command"');
          }

          // Traiter la commande
          _processCommand(command);
        },
        listenFor: const Duration(seconds: 10), // Écoute pendant 10 secondes
        pauseFor: const Duration(seconds: 2),
        localeId: 'fr_FR',
        listenOptions: SpeechListenOptions(
          partialResults: false, // Seulement les résultats finaux pour les commandes
          listenMode: ListenMode.confirmation,
        ),
      );

      _isListening = true;
      _listeningController?.add(true);

      if (kDebugMode) {
        print('VoiceCommandService: Écoute des commandes démarrée');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('VoiceCommandService: Erreur démarrage écoute: $e');
      }
      return false;
    }
  }

  /// Arrête l'écoute des commandes vocales
  Future<void> stopListening() async {
    try {
      if (_isListening) {
        await _speechToText.stop();
        _isListening = false;
        _listeningController?.add(false);
        
        if (kDebugMode) {
          print('VoiceCommandService: Écoute arrêtée');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('VoiceCommandService: Erreur arrêt écoute: $e');
      }
    }
  }

  /// Traite une commande vocale avec reconnaissance améliorée
  void _processCommand(String command) {
    // Nettoyer la commande (supprimer les mots parasites)
    String cleanCommand = _cleanCommand(command);

    if (kDebugMode) {
      print('VoiceCommandService: Commande originale: "$command"');
      print('VoiceCommandService: Commande nettoyée: "$cleanCommand"');
    }

    // Chercher une correspondance exacte
    VoiceCommand? matchedCommand = _commands[cleanCommand];

    // Si pas de correspondance exacte, chercher une correspondance partielle intelligente
    matchedCommand ??= _findBestMatch(cleanCommand);

    if (matchedCommand != null) {
      if (kDebugMode) {
        print('VoiceCommandService: ✅ Commande reconnue: ${matchedCommand.action} (${matchedCommand.description})');
      }

      // Prononcer la réponse vocale
      if (_ttsService.isInitialized) {
        _ttsService.speakCommandFeedback(matchedCommand.action);
      }

      // Envoyer la commande via le stream
      _commandController?.add(matchedCommand.action);
    } else {
      if (kDebugMode) {
        print('VoiceCommandService: ❌ Commande non reconnue: "$command"');
      }

      // Prononcer l'erreur
      if (_ttsService.isInitialized) {
        _ttsService.speakCommandFeedback('unknown:$command');
      }

      // Envoyer une commande inconnue
      _commandController?.add('unknown:$command');
    }
  }

  /// Nettoie la commande en supprimant les mots parasites
  String _cleanCommand(String command) {
    // Mots parasites à supprimer
    final stopWords = [
      'je', 'veux', 'voudrais', 'peux', 'tu', 'pourrais', 'aller', 'à', 'la', 'le', 'les', 'des', 'du', 'de',
      'ouvrir', 'afficher', 'montrer', 'voir', 'naviguer', 'vers', 'sur', 'dans', 'pour', 'avec', 'sans',
      'maintenant', 'tout', 'de', 'suite', 'rapidement', 'vite', 'please', 's\'il', 'vous', 'plaît'
    ];

    List<String> words = command.toLowerCase().split(' ');
    words = words.where((word) => !stopWords.contains(word) && word.isNotEmpty).toList();

    return words.join(' ').trim();
  }

  /// Trouve la meilleure correspondance pour une commande
  VoiceCommand? _findBestMatch(String command) {
    VoiceCommand? bestMatch;
    int bestScore = 0;

    for (final entry in _commands.entries) {
      int score = _calculateSimilarity(command, entry.key);

      if (score > bestScore && score >= 60) { // Seuil de 60% de similarité
        bestScore = score;
        bestMatch = entry.value;
      }
    }

    if (bestMatch != null) {
      logger.info('🎤 Meilleure correspondance: ${bestMatch.action} (score: $bestScore%)');
    }

    return bestMatch;
  }

  /// Calcule la similarité entre deux chaînes (en pourcentage)
  int _calculateSimilarity(String command, String keyword) {
    // Correspondance exacte
    if (command == keyword) return 100;

    // Contient le mot-clé
    if (command.contains(keyword) || keyword.contains(command)) {
      return 80;
    }

    // Correspondance partielle avec mots communs
    List<String> commandWords = command.split(' ');
    List<String> keywordWords = keyword.split(' ');

    int commonWords = 0;
    for (String word in commandWords) {
      if (keywordWords.contains(word)) {
        commonWords++;
      }
    }

    if (commonWords > 0) {
      return (commonWords * 100 / keywordWords.length).round();
    }

    // Correspondance par caractères (distance de Levenshtein simplifiée)
    return _levenshteinSimilarity(command, keyword);
  }

  /// Calcule la similarité basée sur la distance de Levenshtein
  int _levenshteinSimilarity(String s1, String s2) {
    if (s1.length < s2.length) {
      String temp = s1;
      s1 = s2;
      s2 = temp;
    }

    int distance = _levenshteinDistance(s1, s2);
    int maxLength = s1.length;

    if (maxLength == 0) return 100;

    return ((maxLength - distance) * 100 / maxLength).round();
  }

  /// Calcule la distance de Levenshtein entre deux chaînes
  int _levenshteinDistance(String s1, String s2) {
    List<List<int>> matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }

    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        int cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,      // deletion
          matrix[i][j - 1] + 1,      // insertion
          matrix[i - 1][j - 1] + cost // substitution
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }

  /// Obtient la liste des commandes disponibles
  List<VoiceCommand> getAvailableCommands() {
    return _commands.values.toSet().toList();
  }

  /// Obtient les commandes par type
  List<VoiceCommand> getCommandsByType(VoiceCommandType type) {
    return _commands.values.where((cmd) => cmd.type == type).toSet().toList();
  }

  /// Libère les ressources
  Future<void> dispose() async {
    try {
      await stopListening();
      _ttsService.dispose();
      await _commandController?.close();
      await _listeningController?.close();

      _isInitialized = false;
      _lastCommand = '';

      if (kDebugMode) {
        print('VoiceCommandService: Ressources libérées');
      }
    } catch (e) {
      if (kDebugMode) {
        print('VoiceCommandService: Erreur libération ressources: $e');
      }
    }
  }
}

/// Types de commandes vocales
enum VoiceCommandType {
  navigation,
  action,
  control,
  info,
}

/// Représente une commande vocale
class VoiceCommand {
  final String action;
  final String description;
  final VoiceCommandType type;

  VoiceCommand(this.action, this.description, this.type);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VoiceCommand &&
          runtimeType == other.runtimeType &&
          action == other.action;

  @override
  int get hashCode => action.hashCode;

  @override
  String toString() {
    return 'VoiceCommand(action: $action, description: $description, type: $type)';
  }
}
