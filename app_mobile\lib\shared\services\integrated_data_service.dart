import 'package:flutter/foundation.dart';
import '../models/contact.dart';
import '../models/message.dart';
import '../models/call_record.dart';
import '../services/call_service.dart';
import '../services/contact_service.dart';
import '../services/message_service.dart';
import '../services/device_data_service.dart';

class IntegratedDataService {
  static final IntegratedDataService _instance = IntegratedDataService._internal();
  factory IntegratedDataService() => _instance;
  IntegratedDataService._internal();

  final ContactService _contactService = ContactService();
  final CallService _callService = CallService();
  final MessageService _messageService = MessageService();
  final DeviceDataService _deviceDataService = DeviceDataService();

  bool _isInitialized = false;
  bool _deviceAccessEnabled = false;

  /// Initialise tous les services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialiser les services locaux
      await _contactService.initialize();
      await _callService.initialize();
      await _messageService.initialize();

      // Tenter d'initialiser l'accès aux données de l'appareil
      try {
        _deviceAccessEnabled = await _deviceDataService.initialize();

        if (_deviceAccessEnabled) {
          // Synchroniser les données de l'appareil avec nos services locaux
          await _syncDeviceData();
        }
      } catch (e) {
        if (kDebugMode) {
          print('IntegratedDataService: Erreur accès appareil: $e');
        }
        _deviceAccessEnabled = false;
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('IntegratedDataService: Initialisé - Accès appareil: $_deviceAccessEnabled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('IntegratedDataService: Erreur initialisation: $e');
      }
    }
  }

  /// Synchronise les données de l'appareil avec nos services locaux
  Future<void> _syncDeviceData() async {
    if (!_deviceAccessEnabled) return;

    try {
      // Synchroniser les contacts
      await _syncContacts();
      
      // Synchroniser l'historique des appels
      await _syncCallHistory();
      
      // Synchroniser les messages
      await _syncMessages();

      if (kDebugMode) {
        print('IntegratedDataService: Synchronisation terminée');
      }
    } catch (e) {
      if (kDebugMode) {
        print('IntegratedDataService: Erreur synchronisation: $e');
      }
    }
  }

  /// Synchronise les contacts de l'appareil
  Future<void> _syncContacts() async {
    try {
      final deviceContacts = await _deviceDataService.getDeviceContacts();
      
      for (final deviceContact in deviceContacts) {
        // Vérifier si le contact existe déjà
        final existingContact = _contactService.contacts.firstWhere(
          (c) => c.phone == deviceContact.phone,
          orElse: () => Contact(
            id: '',
            name: '',
            phone: '',
            type: ContactType.personal,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        if (existingContact.id.isEmpty) {
          // Nouveau contact, l'ajouter
          await _contactService.addContact(deviceContact);
        } else {
          // Contact existant, mettre à jour si nécessaire
          if (existingContact.name != deviceContact.name ||
              existingContact.email != deviceContact.email) {
            await _contactService.updateContact(
              existingContact.copyWith(
                name: deviceContact.name,
                email: deviceContact.email,
              ),
            );
          }
        }
      }

      if (kDebugMode) {
        print('IntegratedDataService: ${deviceContacts.length} contacts synchronisés');
      }
    } catch (e) {
      if (kDebugMode) {
        print('IntegratedDataService: Erreur sync contacts: $e');
      }
    }
  }

  /// Synchronise l'historique des appels
  Future<void> _syncCallHistory() async {
    try {
      final deviceCalls = await _deviceDataService.getDeviceCallHistory();
      
      // Les appels de l'appareil remplacent notre historique local
      // car ils sont plus précis et complets
      await _callService.clearCallHistory();
      
      for (final call in deviceCalls) {
        // Ajouter chaque appel à notre service local
        // Note: Nous devons adapter cela car CallService n'a pas de méthode addCallRecord
        // Pour l'instant, nous gardons juste les données en mémoire
      }

      if (kDebugMode) {
        print('IntegratedDataService: ${deviceCalls.length} appels synchronisés');
      }
    } catch (e) {
      if (kDebugMode) {
        print('IntegratedDataService: Erreur sync appels: $e');
      }
    }
  }

  /// Synchronise les messages
  Future<void> _syncMessages() async {
    try {
      final deviceMessages = await _deviceDataService.getDeviceSMS();
      
      for (final deviceMessage in deviceMessages) {
        // Vérifier si le message existe déjà
        final existingMessages = _messageService.messages.where(
          (m) => m.content == deviceMessage.content && 
                 m.sentAt.difference(deviceMessage.sentAt).abs().inMinutes < 1,
        );

        if (existingMessages.isEmpty) {
          // Nouveau message, l'ajouter
          await _messageService.sendMessage(deviceMessage);
        }
      }

      if (kDebugMode) {
        print('IntegratedDataService: ${deviceMessages.length} messages synchronisés');
      }
    } catch (e) {
      if (kDebugMode) {
        print('IntegratedDataService: Erreur sync messages: $e');
      }
    }
  }

  /// Obtient tous les contacts (locaux + appareil)
  Future<List<Contact>> getAllContacts() async {
    final localContacts = _contactService.contacts;
    
    if (_deviceAccessEnabled) {
      try {
        final deviceContacts = await _deviceDataService.getDeviceContacts();
        
        // Fusionner les contacts en évitant les doublons
        final Map<String, Contact> contactMap = {};
        
        // Ajouter les contacts locaux
        for (final contact in localContacts) {
          contactMap[contact.phone] = contact;
        }
        
        // Ajouter les contacts de l'appareil (en priorité)
        for (final contact in deviceContacts) {
          contactMap[contact.phone] = contact;
        }
        
        return contactMap.values.toList();
      } catch (e) {
        if (kDebugMode) {
          print('IntegratedDataService: Erreur récupération contacts appareil: $e');
        }
      }
    }
    
    return localContacts;
  }

  /// Obtient tout l'historique des appels (local + appareil)
  Future<List<CallRecord>> getAllCallHistory() async {
    if (_deviceAccessEnabled) {
      try {
        // Prioriser les données de l'appareil car elles sont plus complètes
        return await _deviceDataService.getDeviceCallHistory();
      } catch (e) {
        if (kDebugMode) {
          print('IntegratedDataService: Erreur récupération appels appareil: $e');
        }
      }
    }
    
    // Fallback vers les données locales
    return _callService.callHistory;
  }

  /// Obtient tous les messages (locaux + appareil)
  Future<List<Message>> getAllMessages() async {
    final localMessages = _messageService.messages;
    
    if (_deviceAccessEnabled) {
      try {
        final deviceMessages = await _deviceDataService.getDeviceSMS();
        
        // Fusionner les messages en évitant les doublons
        final Map<String, Message> messageMap = {};
        
        // Ajouter les messages locaux
        for (final message in localMessages) {
          final key = '${message.contactId}_${message.sentAt.millisecondsSinceEpoch}';
          messageMap[key] = message;
        }
        
        // Ajouter les messages de l'appareil
        for (final message in deviceMessages) {
          final key = '${message.contactId}_${message.sentAt.millisecondsSinceEpoch}';
          if (!messageMap.containsKey(key)) {
            messageMap[key] = message;
          }
        }
        
        final allMessages = messageMap.values.toList();
        allMessages.sort((a, b) => a.sentAt.compareTo(b.sentAt));
        
        return allMessages;
      } catch (e) {
        if (kDebugMode) {
          print('IntegratedDataService: Erreur récupération messages appareil: $e');
        }
      }
    }
    
    return localMessages;
  }

  /// Envoie un SMS en utilisant l'API native si disponible
  Future<bool> sendSMS(String phoneNumber, String message) async {
    if (_deviceAccessEnabled) {
      try {
        final success = await _deviceDataService.sendDeviceSMS(phoneNumber, message);
        if (success) {
          // Enregistrer aussi localement
          final localMessage = Message(
            id: '',
            contactId: _getContactIdFromPhone(phoneNumber),
            content: message,
            type: MessageType.text,
            status: MessageStatus.sent,
            isFromMe: true,
            sentAt: DateTime.now(),
          );
          
          await _messageService.sendMessage(localMessage);
          return true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('IntegratedDataService: Erreur envoi SMS appareil: $e');
        }
      }
    }
    
    // Fallback vers l'envoi local (URL launcher)
    return await _messageService.sendSMS(phoneNumber, message);
  }

  /// Obtient le statut des permissions
  Future<Map<String, bool>> getPermissionsStatus() async {
    if (_deviceAccessEnabled) {
      return await _deviceDataService.getPermissionsStatus();
    }
    
    return {
      'phone': false,
      'sms': false,
      'contacts': false,
    };
  }

  /// Demande les permissions et réinitialise si accordées
  Future<bool> requestPermissions() async {
    final success = await _deviceDataService.initialize();
    
    if (success && !_deviceAccessEnabled) {
      _deviceAccessEnabled = true;
      await _syncDeviceData();
    }
    
    return success;
  }

  /// Génère un ID de contact basé sur le numéro de téléphone
  String _getContactIdFromPhone(String phoneNumber) {
    return 'phone_${phoneNumber.replaceAll(RegExp(r'[^\d]'), '')}';
  }

  /// Getters pour accéder aux services individuels
  ContactService get contactService => _contactService;
  CallService get callService => _callService;
  MessageService get messageService => _messageService;
  DeviceDataService get deviceDataService => _deviceDataService;
  
  bool get isDeviceAccessEnabled => _deviceAccessEnabled;
  bool get isInitialized => _isInitialized;

  /// Nettoie les ressources
  Future<void> dispose() async {
    await _contactService.dispose();
    await _callService.dispose();
    await _messageService.dispose();
    await _deviceDataService.dispose();
    
    _isInitialized = false;
    _deviceAccessEnabled = false;
    
    if (kDebugMode) {
      print('IntegratedDataService: Ressources libérées');
    }
  }
}
