import 'package:canne_connectee/core/services/notification_service.dart';
import 'package:flutter/material.dart';

class CanneConnectionPage extends StatefulWidget {
  const CanneConnectionPage({super.key});

  @override
  State<CanneConnectionPage> createState() => _CanneConnectionPageState();
}

class _CanneConnectionPageState extends State<CanneConnectionPage> {
  final NotificationService _notificationService = NotificationService();
  bool _isConnected = false;
  bool _isConnecting = false;
  String _deviceName = '';
  String _batteryLevel = '';
  String _signalStrength = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Connexion Canne',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // Statut de connexion
            _buildConnectionStatus(),
            
            const SizedBox(height: 30),
            
            // Informations de la canne (si connectée)
            if (_isConnected) ...[
              _buildDeviceInfo(),
              const SizedBox(height: 30),
            ],
            
            // Bouton de connexion/déconnexion
            _buildConnectionButton(),
            
            const SizedBox(height: 30),
            
            // Instructions
            _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Icône de statut
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _isConnected 
                  ? Colors.green.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _isConnected ? Icons.bluetooth_connected : Icons.bluetooth_disabled,
              size: 40,
              color: _isConnected ? Colors.green : Colors.grey,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Texte de statut
          Text(
            _isConnected ? 'Canne Connectée' : 'Canne Déconnectée',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: _isConnected ? Colors.green : Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            _isConnected 
                ? 'Votre canne est connectée et prête à utiliser'
                : 'Connectez votre canne pour accéder à toutes les fonctionnalités',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informations de la canne',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFFF7900),
            ),
          ),
          
          const SizedBox(height: 16),
          
          _buildInfoRow(Icons.devices, 'Nom de l\'appareil', _deviceName.isEmpty ? 'Canne Connectée v1.0' : _deviceName),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.battery_full, 'Niveau de batterie', _batteryLevel.isEmpty ? '85%' : _batteryLevel),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.signal_cellular_4_bar, 'Force du signal', _signalStrength.isEmpty ? 'Excellent' : _signalStrength),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isConnecting ? null : _toggleConnection,
        style: ElevatedButton.styleFrom(
          backgroundColor: _isConnected ? Colors.red : const Color(0xFFFF7900),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: _isConnecting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    'Connexion en cours...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              )
            : Text(
                _isConnected ? 'Déconnecter la canne' : 'Connecter la canne',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFFF7900).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.info_outline, color: Color(0xFFFF7900)),
              SizedBox(width: 8),
              Text(
                'Instructions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFF7900),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          const Text(
            '1. Assurez-vous que votre canne est allumée\n'
            '2. Activez le Bluetooth sur votre téléphone\n'
            '3. Appuyez sur "Connecter la canne"\n'
            '4. Suivez les instructions à l\'écran',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFFFF7900),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleConnection() async {
    setState(() {
      _isConnecting = true;
    });

    // Simuler la connexion/déconnexion
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isConnected = !_isConnected;
      _isConnecting = false;
      
      if (_isConnected) {
        _deviceName = 'Canne Connectée v1.0';
        _batteryLevel = '85%';
        _signalStrength = 'Excellent';
      } else {
        _deviceName = '';
        _batteryLevel = '';
        _signalStrength = '';
      }
    });

    // Ajouter une notification
    _notificationService.addCanneConnectionNotification(_isConnected);

    // Afficher un message de confirmation
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isConnected
                ? 'Canne connectée avec succès !'
                : 'Canne déconnectée',
          ),
          backgroundColor: _isConnected ? Colors.green : Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
