# 🔐 Système d'Authentification - Canne Connectée

## 📋 Vue d'ensemble

Le système d'authentification de l'application Canne Connectée gère deux types d'utilisateurs distincts avec des interfaces et fonctionnalités spécifiques :

- **👥 Proche/Aidant** : Interface de surveillance et d'assistance
- **🦯 Aveugle/Malvoyant** : Interface optimisée avec commandes vocales et navigation assistée

## 🏗️ Architecture

### 📁 Structure des fichiers

```
lib/features/auth/
├── services/
│   └── auth_service.dart           # Service d'authentification principal
├── pages/
│   ├── simple_user_type_selection_page.dart  # Sélection du type d'utilisateur
│   ├── login_page.dart             # Page de connexion
│   ├── register_page.dart          # Page d'inscription (ancienne)
│   └── register_with_type_page.dart # Page d'inscription avec type
├── widgets/
│   └── auth_wrapper.dart           # Wrapper de redirection
└── models/ → shared/models/
    └── user.dart                   # Modèle utilisateur avec types

lib/features/proche/
└── pages/
    └── proche_home_page.dart       # Interface pour les proches

lib/core/routing/
└── app_router.dart                 # Gestionnaire de routage conditionnel
```

## 🔄 Flux d'authentification

### 1. **Démarrage de l'application**
```
main.dart → AuthWrapper → SimpleUserTypeSelectionPage
```

### 2. **Sélection du type d'utilisateur**
- L'utilisateur choisit son profil (Proche ou Aveugle)
- Redirection vers `RegisterWithTypePage` avec le type sélectionné

### 3. **Inscription**
- Formulaire adapté au type d'utilisateur
- Envoi des données à l'API avec le `user_type`
- Sauvegarde locale du token et des informations utilisateur

### 4. **Redirection conditionnelle**
```
AuthWrapper vérifie le type d'utilisateur :
├── UserType.proche → ProcheHomePage
└── UserType.aveugle → HomePage (interface existante)
```

## 🎯 Types d'utilisateurs

### 👥 Proche/Aidant
**Fonctionnalités accessibles :**
- 📊 Tableau de bord de surveillance
- 📍 Suivi de localisation en temps réel
- 📞 Appels et messages
- 🏥 Surveillance de santé
- 🚨 Gestion des alertes

**Interface :** `ProcheHomePage`
- Navigation par onglets
- Actions rapides (appeler, localiser, alerter)
- Historique des activités
- Statut en temps réel

### 🦯 Aveugle/Malvoyant
**Fonctionnalités accessibles :**
- 🎤 Commandes vocales
- 🆘 Alertes SOS
- 🧭 Navigation assistée
- 📷 Détection d'obstacles
- 🔗 Connexion canne

**Interface :** `HomePage` (existante)
- Optimisée pour l'accessibilité
- Commandes vocales intégrées
- Interface tactile simplifiée

## 🔧 Services et composants

### AuthService
**Méthodes principales :**
```dart
// Authentification
Future<Map<String, dynamic>> login(String email, String password)
Future<Map<String, dynamic>> register({...})

// Gestion des types
UserType? get currentUserType
bool get isProche
bool get isAveugle

// Permissions
bool hasAccess(String feature)
bool canAccessRoute(String route)
String getHomeRouteForUserType()
```

### User Model
**Propriétés :**
```dart
class User {
  final String id;
  final String nomUtilisateur;
  final String prenomUtilisateur;
  final String emailUtilisateur;
  final UserType userType;  // ← Nouveau champ
  // ...
  
  // Méthodes utilitaires
  bool get isProche
  bool get isAveugle
  bool hasAccessTo(String feature)
}
```

### AuthWrapper
**Logique de redirection :**
```dart
if (!isLoggedIn) {
  return SimpleUserTypeSelectionPage();
}

switch (user.userType) {
  case UserType.proche:
    return ProcheHomePage();
  case UserType.aveugle:
    return HomePage(); // Interface existante
}
```

## 🛡️ Gestion des permissions

### Système de permissions par fonctionnalité

```dart
// Fonctionnalités réservées aux aveugles/malvoyants
'voice_commands', 'sos_alerts', 'navigation_assistance', 
'obstacle_detection', 'cane_connection'

// Fonctionnalités réservées aux proches/aidants
'monitoring', 'location_tracking', 'emergency_contacts', 
'health_reports'

// Fonctionnalités communes
'calls', 'messages', 'profile', 'settings'
```

### Utilisation
```dart
// Vérifier l'accès à une fonctionnalité
if (authService.hasAccess('voice_commands')) {
  // Afficher les commandes vocales
}

// Protéger une page
FeatureGuard(
  feature: 'monitoring',
  child: MonitoringPage(),
  fallback: AccessDeniedPage(),
)
```

## 🔗 Intégration API

### Endpoints utilisés
```
POST /api/auth/login
POST /api/auth/register  (avec user_type)
POST /api/auth/logout
GET  /api/auth/validate
```

### Format des données
```json
// Inscription
{
  "nom_utilisateur": "Dupont",
  "prenom_utilisateur": "Jean",
  "email_utilisateur": "<EMAIL>",
  "mot_de_passe": "password123",
  "user_type": "aveugle",  // ou "proche"
  "contact_utilisateur": "**********",
  "adresse_utilisateur": "123 Rue Example"
}

// Réponse
{
  "success": true,
  "token": "jwt_token_here",
  "user": {
    "id": "1",
    "nom_utilisateur": "Dupont",
    "user_type": "aveugle",
    // ...
  }
}
```

## 🚀 Utilisation

### 1. **Première utilisation**
1. L'utilisateur lance l'app
2. Sélectionne son type (Proche ou Aveugle)
3. Remplit le formulaire d'inscription
4. Est automatiquement redirigé vers son interface

### 2. **Utilisateur existant**
1. L'utilisateur lance l'app
2. AuthWrapper vérifie le token local
3. Redirection automatique vers l'interface appropriée

### 3. **Changement de compte**
1. Déconnexion via le menu
2. Retour à la sélection du type d'utilisateur
3. Possibilité de se connecter avec un autre compte

## 🔄 États de l'application

```
Non authentifié → SimpleUserTypeSelectionPage
├── Inscription → RegisterWithTypePage → Interface utilisateur
└── Connexion → LoginPage → Interface utilisateur

Authentifié → Redirection automatique
├── Proche → ProcheHomePage
└── Aveugle → HomePage
```

## 🎯 Avantages du système

1. **Séparation claire** des interfaces selon les besoins
2. **Sécurité** avec gestion des permissions par type
3. **Flexibilité** pour ajouter de nouveaux types d'utilisateurs
4. **UX optimisée** pour chaque profil d'utilisateur
5. **Maintenance facilitée** avec une architecture modulaire

## 🔧 Configuration

### Variables importantes
```dart
// AuthService
static const String _baseUrl = 'http://***************:3000/api';

// Types d'utilisateurs
enum UserType { proche, aveugle }
```

Le système est maintenant prêt et fonctionnel ! 🎉
