import 'package:canne_connectee/features/auth/widgets/auth_wrapper.dart';
import 'package:canne_connectee/shared/models/proche.dart';
import 'package:canne_connectee/shared/services/proche_service.dart';
import 'package:flutter/material.dart';
import 'add_proche_page.dart';

class ProchesPage extends StatefulWidget {
  const ProchesPage({super.key});

  @override
  State<ProchesPage> createState() => _ProchesPageState();
}

class _ProchesPageState extends State<ProchesPage> {
  final ProcheService _procheService = ProcheService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProches();
  }

  Future<void> _loadProches() async {
    setState(() {
      _isLoading = true;
    });

    final result = await _procheService.getProches();
    
    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (!result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message']),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FeatureGuard(
      feature: 'contacts',
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          title: const Text(
            'Mes Proches',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: const Color(0xFFFF7900),
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            IconButton(
              onPressed: _loadProches,
              icon: const Icon(Icons.refresh),
            ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFFFF7900),
                ),
              )
            : _buildBody(),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => _navigateToAddProche(),
          backgroundColor: const Color(0xFFFF7900),
          foregroundColor: Colors.white,
          icon: const Icon(Icons.person_add),
          label: const Text('Inviter un proche'),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final proches = _procheService.proches;

    if (proches.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadProches,
      color: const Color(0xFFFF7900),
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Statistiques
          _buildStatsCards(),
          
          const SizedBox(height: 24),
          
          // Liste des proches
          _buildProchesList(proches),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFFFF7900).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.people_outline,
                size: 64,
                color: Color(0xFFFF7900),
              ),
            ),
            
            const SizedBox(height: 24),
            
            const Text(
              'Aucun proche ajouté',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 12),
            
            const Text(
              'Invitez vos proches et aidants pour qu\'ils puissent vous suivre et vous assister en cas de besoin.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
                height: 1.4,
              ),
            ),
            
            const SizedBox(height: 32),
            
            ElevatedButton.icon(
              onPressed: () => _navigateToAddProche(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: const Icon(Icons.person_add),
              label: const Text(
                'Inviter mon premier proche',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    final proches = _procheService.proches;
    final connected = _procheService.getConnectedProches().length;
    final pending = _procheService.getPendingInvitations().length;
    final urgence = _procheService.getProchesByPriority(ProchePriority.urgence).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total',
            value: proches.length.toString(),
            icon: Icons.people,
            color: const Color(0xFFFF7900),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'Connectés',
            value: connected.toString(),
            icon: Icons.check_circle,
            color: Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'En attente',
            value: pending.toString(),
            icon: Icons.pending,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'Urgence',
            value: urgence.toString(),
            icon: Icons.priority_high,
            color: Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProchesList(List<Proche> proches) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Mes proches',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 16),
        
        ...proches.map((proche) => _buildProcheCard(proche)),
      ],
    );
  }

  Widget _buildProcheCard(Proche proche) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: proche.isConnected 
              ? Colors.green.withOpacity(0.3)
              : proche.isPending 
                  ? Colors.orange.withOpacity(0.3)
                  : Colors.grey.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: proche.isConnected 
                      ? Colors.green.withOpacity(0.1)
                      : proche.isPending 
                          ? Colors.orange.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  proche.relationType.icon,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      proche.fullName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          proche.relationType.displayName,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: proche.isConnected 
                                ? Colors.green
                                : proche.isPending 
                                    ? Colors.orange
                                    : Colors.grey,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            proche.statusText,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              PopupMenuButton<String>(
                onSelected: (value) => _handleProcheAction(value, proche),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 18),
                        SizedBox(width: 8),
                        Text('Modifier'),
                      ],
                    ),
                  ),
                  if (proche.isPending)
                    const PopupMenuItem(
                      value: 'resend',
                      child: Row(
                        children: [
                          Icon(Icons.send, size: 18),
                          SizedBox(width: 8),
                          Text('Renvoyer invitation'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 18, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Supprimer', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          if (proche.email.isNotEmpty) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.email, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  proche.email,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
          
          if (proche.telephone != null && proche.telephone!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  proche.telephone!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _handleProcheAction(String action, Proche proche) {
    switch (action) {
      case 'edit':
        // TODO: Naviguer vers la page d'édition
        break;
      case 'resend':
        _resendInvitation(proche);
        break;
      case 'delete':
        _confirmDelete(proche);
        break;
    }
  }

  Future<void> _resendInvitation(Proche proche) async {
    final result = await _procheService.renvoyerInvitation(proche.id);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: result['success'] ? Colors.green : Colors.red,
        ),
      );
      
      if (result['success']) {
        setState(() {});
      }
    }
  }

  void _confirmDelete(Proche proche) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le proche'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer ${proche.fullName} de votre liste de proches ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteProche(proche);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteProche(Proche proche) async {
    final result = await _procheService.supprimerProche(proche.id);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: result['success'] ? Colors.green : Colors.red,
        ),
      );
      
      if (result['success']) {
        setState(() {});
      }
    }
  }

  void _navigateToAddProche() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProchePage(),
      ),
    ).then((_) => _loadProches());
  }
}
