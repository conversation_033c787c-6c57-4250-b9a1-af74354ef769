# Architecture du Projet - Canne Connectée

## 📁 Structure Organisée

Le projet a été réorganisé selon une architecture modulaire par domaines fonctionnels pour améliorer la maintenabilité et la scalabilité.

### 🏗️ Structure Générale

```
lib/
├── core/                          # Fonctionnalités centrales
│   ├── config/                    # Configuration de l'application
│   ├── constants/                 # Constantes globales
│   ├── logging/                   # Système de logging
│   ├── services/                  # Services core (API, cache, connectivity)
│   └── app_initializer.dart       # Initialisation de l'application
├── features/                      # Fonctionnalités par domaine
│   ├── auth/                      # Authentification
│   ├── communication/             # Appels, messages, MQTT
│   ├── location/                  # GPS, trajets, cartes
│   ├── voice/                     # Services vocaux
│   ├── sos/                       # Fonctionnalités SOS
│   └── hardware/                  # Caméra, capteurs, notifications
├── shared/                        # Éléments partagés
│   ├── models/                    # Modèles de données
│   ├── services/                  # Services partagés
│   ├── widgets/                   # Widgets réutilisables
│   └── utils/                     # Utilitaires
├── main.dart                      # Point d'entrée
└── homepage.dart                  # Page principale
```

### 🎯 Domaines Fonctionnels

#### 🔐 Auth (Authentification)
- **Pages** : login, register, user_type_selection
- **Services** : auth_service
- **Widgets** : auth_wrapper

#### 📞 Communication
- **Pages** : appels, messages, proches
- **Services** : call_service, message_service, mqtt_service
- **Widgets** : (à venir)

#### 📍 Location (Localisation)
- **Pages** : real_time_location, historique
- **Services** : gps_services, journey_service
- **Widgets** : map_widget, position_marker

#### 🎤 Voice (Services Vocaux)
- **Pages** : voice_commands_help
- **Services** : text_to_speech_service, voice_command_service, voice_service, speech_services
- **Widgets** : voice_assistant_widget

#### 🆘 SOS
- **Pages** : sos
- **Services** : sos_service
- **Widgets** : sos_widget, sos_camera_widget, sos_history_widget

#### 🔧 Hardware
- **Pages** : canne_connection, notifications
- **Services** : camera_service, device_data_service, health_service, obstacle_service
- **Widgets** : camera_widget, obstacle_marker, permissions_status_widget

### 🔄 Services Consolidés

#### Doublons Supprimés :
- ❌ `text_to_speech_service.dart` (version basique)
- ✅ `text_to_speech_service.dart` (version améliorée renommée)
- ❌ `location_service.dart` (version simplifiée)
- ✅ `gps_services.dart` (version complète)
- ❌ `home_content.dart` et `home_page_content.dart` (doublons)
- ✅ `home_content_widget.dart` (version unifiée)

### 📦 Services par Catégorie

#### Core Services
- `api_service.dart` - API centrale
- `notification_service.dart` - Notifications système
- `cache_service.dart` - Gestion du cache
- `connectivity_service.dart` - Connectivité réseau
- `service_manager.dart` - Gestionnaire de services

#### Shared Services
- `contact_service.dart` - Gestion des contacts
- `proche_service.dart` - Gestion des proches
- `integrated_data_service.dart` - Intégration des données

### 🎨 Widgets Organisés

#### Par Domaine
- **Auth** : auth_wrapper
- **Location** : map_widget, position_marker
- **Voice** : voice_assistant_widget
- **SOS** : sos_widget, sos_camera_widget, sos_history_widget
- **Hardware** : camera_widget, obstacle_marker, permissions_status_widget

#### Shared
- **Widgets** : home_content_widget (widget principal de la page d'accueil)

### 📋 Modèles de Données

Tous les modèles sont centralisés dans `shared/models/` :
- `user.dart` - Modèle utilisateur
- `contact.dart` - Modèle contact
- `message.dart` - Modèle message
- `call_record.dart` - Modèle d'enregistrement d'appel
- `proche.dart` - Modèle proche

### 🔧 Avantages de cette Architecture

1. **Séparation des responsabilités** : Chaque domaine a ses propres services, pages et widgets
2. **Réutilisabilité** : Les éléments partagés sont centralisés
3. **Maintenabilité** : Plus facile de localiser et modifier le code
4. **Scalabilité** : Facile d'ajouter de nouvelles fonctionnalités
5. **Testabilité** : Chaque domaine peut être testé indépendamment

### 📝 Conventions de Nommage

- **Dossiers** : snake_case (ex: `voice_services`)
- **Fichiers** : snake_case (ex: `text_to_speech_service.dart`)
- **Classes** : PascalCase (ex: `TextToSpeechService`)
- **Variables** : camelCase (ex: `isInitialized`)

### 🚀 Prochaines Étapes

1. ✅ Structure créée
2. ✅ Doublons supprimés
3. ✅ Services réorganisés
4. ✅ Pages et widgets déplacés
5. 🔄 Mise à jour des imports (en cours)
6. ⏳ Tests et validation
7. ⏳ Documentation des APIs
