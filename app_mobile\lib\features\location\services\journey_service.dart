import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'mqtt_service.dart';

class JourneyPoint {
  final LatLng position;
  final DateTime timestamp;
  final String? locationName;
  final double? speed;
  final double? accuracy;

  JourneyPoint({
    required this.position,
    required this.timestamp,
    this.locationName,
    this.speed,
    this.accuracy,
  });

  Map<String, dynamic> toJson() => {
    'latitude': position.latitude,
    'longitude': position.longitude,
    'timestamp': timestamp.millisecondsSinceEpoch,
    'locationName': locationName,
    'speed': speed,
    'accuracy': accuracy,
  };

  factory JourneyPoint.fromJson(Map<String, dynamic> json) => JourneyPoint(
    position: LatLng(json['latitude'], json['longitude']),
    timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
    locationName: json['locationName'],
    speed: json['speed']?.toDouble(),
    accuracy: json['accuracy']?.toDouble(),
  );
}

class Journey {
  final String id;
  final List<JourneyPoint> points;
  final DateTime startTime;
  final DateTime? endTime;
  final double totalDistance;
  final String? startLocationName;
  final String? endLocationName;
  final bool isActive;

  Journey({
    required this.id,
    required this.points,
    required this.startTime,
    this.endTime,
    required this.totalDistance,
    this.startLocationName,
    this.endLocationName,
    required this.isActive,
  });

  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  String get formattedDuration {
    final duration = this.duration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}min';
    } else {
      return '${minutes}min';
    }
  }

  String get formattedDistance {
    if (totalDistance < 1000) {
      return '${totalDistance.toStringAsFixed(0)}m';
    } else {
      return '${(totalDistance / 1000).toStringAsFixed(1)}km';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'points': points.map((p) => p.toJson()).toList(),
    'startTime': startTime.millisecondsSinceEpoch,
    'endTime': endTime?.millisecondsSinceEpoch,
    'totalDistance': totalDistance,
    'startLocationName': startLocationName,
    'endLocationName': endLocationName,
    'isActive': isActive,
  };

  factory Journey.fromJson(Map<String, dynamic> json) => Journey(
    id: json['id'],
    points: (json['points'] as List).map((p) => JourneyPoint.fromJson(p)).toList(),
    startTime: DateTime.fromMillisecondsSinceEpoch(json['startTime']),
    endTime: json['endTime'] != null ? DateTime.fromMillisecondsSinceEpoch(json['endTime']) : null,
    totalDistance: json['totalDistance'].toDouble(),
    startLocationName: json['startLocationName'],
    endLocationName: json['endLocationName'],
    isActive: json['isActive'] ?? false,
  );
}

class VisitedPlace {
  final String id;
  final LatLng position;
  final String name;
  final DateTime lastVisited;
  final int visitCount;
  final Duration totalTimeSpent;

  VisitedPlace({
    required this.id,
    required this.position,
    required this.name,
    required this.lastVisited,
    required this.visitCount,
    required this.totalTimeSpent,
  });

  String get formattedLastVisited {
    final now = DateTime.now();
    final difference = now.difference(lastVisited);
    
    if (difference.inDays > 0) {
      return 'Il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return 'Il y a ${difference.inMinutes}min';
    } else {
      return 'À l\'instant';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'latitude': position.latitude,
    'longitude': position.longitude,
    'name': name,
    'lastVisited': lastVisited.millisecondsSinceEpoch,
    'visitCount': visitCount,
    'totalTimeSpent': totalTimeSpent.inMilliseconds,
  };

  factory VisitedPlace.fromJson(Map<String, dynamic> json) => VisitedPlace(
    id: json['id'],
    position: LatLng(json['latitude'], json['longitude']),
    name: json['name'],
    lastVisited: DateTime.fromMillisecondsSinceEpoch(json['lastVisited']),
    visitCount: json['visitCount'],
    totalTimeSpent: Duration(milliseconds: json['totalTimeSpent']),
  );
}

class JourneyService {
  static final JourneyService _instance = JourneyService._internal();
  factory JourneyService() => _instance;
  JourneyService._internal();

  final Distance _distance = const Distance();
  final MqttService _mqttService = MqttService();
  List<Journey> _journeys = [];
  List<VisitedPlace> _visitedPlaces = [];
  Journey? _currentJourney;

  List<Journey> get journeys => List.unmodifiable(_journeys);
  List<VisitedPlace> get visitedPlaces => List.unmodifiable(_visitedPlaces);
  Journey? get currentJourney => _currentJourney;
  Journey? get lastJourney {
    final completedJourneys = _journeys.where((j) => !j.isActive).toList();
    if (completedJourneys.isEmpty) return null;
    return completedJourneys.reduce((a, b) => a.startTime.isAfter(b.startTime) ? a : b);
  }
  VisitedPlace? get lastVisitedPlace {
    if (_visitedPlaces.isEmpty) return null;
    return _visitedPlaces.reduce((a, b) => a.lastVisited.isAfter(b.lastVisited) ? a : b);
  }

  Future<void> initialize() async {
    await _loadData();
    if (kDebugMode) {
      print('JourneyService: Initialisé avec ${_journeys.length} trajets et ${_visitedPlaces.length} lieux');
    }
  }

  Future<void> startJourney(LatLng startPosition, {String? locationName}) async {
    if (_currentJourney != null) {
      await endCurrentJourney();
    }

    final journey = Journey(
      id: 'journey_${DateTime.now().millisecondsSinceEpoch}',
      points: [JourneyPoint(
        position: startPosition,
        timestamp: DateTime.now(),
        locationName: locationName,
      )],
      startTime: DateTime.now(),
      totalDistance: 0.0,
      startLocationName: locationName,
      isActive: true,
    );

    _currentJourney = journey;
    _journeys.add(journey);
    await _saveData();

    // Publier le début du trajet via MQTT
    _mqttService.publishJourneyData(
      journeyId: journey.id,
      action: 'start',
      latitude: startPosition.latitude,
      longitude: startPosition.longitude,
      startLocation: locationName,
    );

    if (kDebugMode) {
      print('JourneyService: Nouveau trajet démarré');
    }
  }

  Future<void> addPointToJourney(LatLng position, {String? locationName, double? speed, double? accuracy}) async {
    if (_currentJourney == null) return;

    final point = JourneyPoint(
      position: position,
      timestamp: DateTime.now(),
      locationName: locationName,
      speed: speed,
      accuracy: accuracy,
    );

    _currentJourney!.points.add(point);

    // Calculer la distance totale
    if (_currentJourney!.points.length > 1) {
      final lastPoint = _currentJourney!.points[_currentJourney!.points.length - 2];
      final distance = _distance.as(LengthUnit.Meter, lastPoint.position, position);
      _currentJourney = Journey(
        id: _currentJourney!.id,
        points: _currentJourney!.points,
        startTime: _currentJourney!.startTime,
        endTime: _currentJourney!.endTime,
        totalDistance: _currentJourney!.totalDistance + distance,
        startLocationName: _currentJourney!.startLocationName,
        endLocationName: _currentJourney!.endLocationName,
        isActive: _currentJourney!.isActive,
      );

      // Mettre à jour dans la liste
      final index = _journeys.indexWhere((j) => j.id == _currentJourney!.id);
      if (index != -1) {
        _journeys[index] = _currentJourney!;
      }

      // Publier la mise à jour du trajet via MQTT
      _mqttService.publishJourneyData(
        journeyId: _currentJourney!.id,
        action: 'update',
        latitude: position.latitude,
        longitude: position.longitude,
        distance: _currentJourney!.totalDistance,
        duration: _currentJourney!.duration.inMinutes,
      );
    }

    await _saveData();
  }

  Future<void> endCurrentJourney({String? endLocationName}) async {
    if (_currentJourney == null) return;

    _currentJourney = Journey(
      id: _currentJourney!.id,
      points: _currentJourney!.points,
      startTime: _currentJourney!.startTime,
      endTime: DateTime.now(),
      totalDistance: _currentJourney!.totalDistance,
      startLocationName: _currentJourney!.startLocationName,
      endLocationName: endLocationName,
      isActive: false,
    );

    // Mettre à jour dans la liste
    final index = _journeys.indexWhere((j) => j.id == _currentJourney!.id);
    if (index != -1) {
      _journeys[index] = _currentJourney!;
    }

    // Ajouter le lieu de fin comme lieu visité
    if (_currentJourney!.points.isNotEmpty) {
      await _addVisitedPlace(
        _currentJourney!.points.last.position,
        endLocationName ?? 'Lieu inconnu',
      );
    }

    // Publier la fin du trajet via MQTT
    _mqttService.publishJourneyData(
      journeyId: _currentJourney!.id,
      action: 'end',
      latitude: _currentJourney!.points.last.position.latitude,
      longitude: _currentJourney!.points.last.position.longitude,
      distance: _currentJourney!.totalDistance,
      duration: _currentJourney!.duration.inMinutes,
      startLocation: _currentJourney!.startLocationName,
      endLocation: endLocationName,
    );

    _currentJourney = null;
    await _saveData();

    if (kDebugMode) {
      print('JourneyService: Trajet terminé');
    }
  }

  Future<void> _addVisitedPlace(LatLng position, String name) async {
    // Vérifier si le lieu existe déjà (dans un rayon de 100m)
    VisitedPlace? existingPlace;
    for (final place in _visitedPlaces) {
      final distance = _distance.as(LengthUnit.Meter, place.position, position);
      if (distance < 100) {
        existingPlace = place;
        break;
      }
    }

    if (existingPlace != null) {
      // Mettre à jour le lieu existant
      final updatedPlace = VisitedPlace(
        id: existingPlace.id,
        position: existingPlace.position,
        name: existingPlace.name,
        lastVisited: DateTime.now(),
        visitCount: existingPlace.visitCount + 1,
        totalTimeSpent: existingPlace.totalTimeSpent,
      );

      final index = _visitedPlaces.indexWhere((p) => p.id == existingPlace!.id);
      if (index != -1) {
        _visitedPlaces[index] = updatedPlace;
      }
    } else {
      // Ajouter un nouveau lieu
      final newPlace = VisitedPlace(
        id: 'place_${DateTime.now().millisecondsSinceEpoch}',
        position: position,
        name: name,
        lastVisited: DateTime.now(),
        visitCount: 1,
        totalTimeSpent: const Duration(),
      );

      _visitedPlaces.add(newPlace);
    }
  }

  Future<void> _loadData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Charger les trajets
      final journeysJson = prefs.getString('journeys');
      if (journeysJson != null) {
        final List<dynamic> journeysList = json.decode(journeysJson);
        _journeys = journeysList.map((j) => Journey.fromJson(j)).toList();
        
        // Trouver le trajet actuel
        for (final journey in _journeys) {
          if (journey.isActive) {
            _currentJourney = journey;
            break;
          }
        }
      }

      // Charger les lieux visités
      final placesJson = prefs.getString('visitedPlaces');
      if (placesJson != null) {
        final List<dynamic> placesList = json.decode(placesJson);
        _visitedPlaces = placesList.map((p) => VisitedPlace.fromJson(p)).toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('JourneyService: Erreur chargement données: $e');
      }
    }
  }

  Future<void> _saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Sauvegarder les trajets
      final journeysJson = json.encode(_journeys.map((j) => j.toJson()).toList());
      await prefs.setString('journeys', journeysJson);

      // Sauvegarder les lieux visités
      final placesJson = json.encode(_visitedPlaces.map((p) => p.toJson()).toList());
      await prefs.setString('visitedPlaces', placesJson);
    } catch (e) {
      if (kDebugMode) {
        print('JourneyService: Erreur sauvegarde données: $e');
      }
    }
  }
}
