import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';

/// Widget de carte GPS avec fonctionnalités de suivi et de navigation
class GpsMapWidget extends StatefulWidget {
  final LatLng? currentPosition;
  final List<LatLng> trace;
  final double totalDistance;
  final bool isTracking;
  final VoidCallback? onToggleTracking;
  final VoidCallback? onClearTrace;
  final Function(LatLng)? onAddPoint;

  const GpsMapWidget({
    super.key,
    this.currentPosition,
    this.trace = const [],
    this.totalDistance = 0.0,
    this.isTracking = true,
    this.onToggleTracking,
    this.onClearTrace,
    this.onAddPoint,
  });

  @override
  State<GpsMapWidget> createState() => _GpsMapWidgetState();
}

class _GpsMapWidgetState extends State<GpsMapWidget> {
  final MapController _mapController = MapController();
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeGps();
  }

  Future<void> _initializeGps() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      setState(() {
        _errorMessage = 'Les services de localisation sont désactivés. Veuillez activer le GPS.';
      });
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        setState(() => _errorMessage = 'Les permissions de localisation sont refusées.');
        return;
      }
      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _errorMessage = 'Les permissions de localisation sont définitivement refusées. Veuillez les activer dans les paramètres.';
        });
        return;
      }
    }
  }

  void _goToMyPosition() {
    if (widget.currentPosition != null) {
      _mapController.move(widget.currentPosition!, 17);
    }
  }

  void _fitTraceToView() {
    if (widget.trace.isEmpty) return;

    if (widget.trace.length == 1) {
      _mapController.move(widget.trace.first, 17);
      return;
    }

    final bounds = LatLngBounds.fromPoints(widget.trace);
    _mapController.fitCamera(
      CameraFit.bounds(bounds: bounds, padding: const EdgeInsets.all(50)),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_errorMessage != null) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white70,
          child: Text(
            _errorMessage!,
            style: const TextStyle(color: Colors.red, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Stack(
      children: [
        _buildMap(),
        // _buildStatsOverlay(),
        _buildControlButtons(),
      ],
    );
  }

  Widget _buildMap() {
    return FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: widget.currentPosition ?? const LatLng(0, 0),
        initialZoom: 17,
        onPositionChanged: (_, __) => setState(() {}),
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          subdomains: const ['a', 'b', 'c'],
        ),
        if (widget.trace.length > 1) _buildPolylineLayer(),
        _buildMarkerLayer(),
      ],
    );
  }

  Widget _buildPolylineLayer() {
    return PolylineLayer(
      polylines: [
        Polyline(
          points: widget.trace,
          strokeWidth: 4.0,
          color: widget.isTracking ? Colors.blueAccent : Colors.grey,
        ),
      ],
    );
  }

  Widget _buildMarkerLayer() {
    return MarkerLayer(
      markers: [
        if (widget.currentPosition != null) _buildCurrentPositionMarker(),
        if (widget.trace.isNotEmpty) _buildStartMarker(),
      ],
    );
  }

  Marker _buildCurrentPositionMarker() {
    return Marker(
      width: 60,
      height: 60,
      point: widget.currentPosition!,
      child: Container(
        decoration: BoxDecoration(
          color: widget.isTracking ? Colors.blue : Colors.grey,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 3),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          widget.isTracking ? Icons.my_location : Icons.location_disabled,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Marker _buildStartMarker() {
    return Marker(
      width: 40,
      height: 40,
      point: widget.trace.first,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.green,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: const Icon(
          Icons.play_arrow,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  // Widget _buildStatsOverlay() {
  //   return Positioned(
  //     top: 50,
  //     left: 16,
  //     right: 16,
  //     child: Container(
  //       padding: const EdgeInsets.all(12),
  //       decoration: BoxDecoration(
  //         color: Colors.white.withOpacity(0.9),
  //         borderRadius: BorderRadius.circular(12),
  //         boxShadow: [
  //           BoxShadow(
  //             color: Colors.black.withOpacity(0.1),
  //             blurRadius: 4,
  //             offset: const Offset(0, 2),
  //           ),
  //         ],
  //       ),
  //       child: Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceAround,
  //         children: [
  //           _buildStatItem('Distance', _formatDistance(widget.totalDistance)),
  //           _buildStatItem('Points', '${widget.trace.length}'),
  //           _buildStatItem('Statut', widget.isTracking ? 'Actif' : 'Pausé'),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: label == 'Statut' 
                ? (widget.isTracking ? Colors.green : Colors.orange)
                : Colors.blueAccent,
          ),
        ),
      ],
    );
  }

  Widget _buildControlButtons() {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildControlButton(
            heroTag: "fit_trace",
            icon: Icons.fit_screen,
            onPressed: widget.trace.length > 1 ? _fitTraceToView : null,
            backgroundColor: widget.trace.length > 1 ? Colors.purple : Colors.grey,
          ),
          const SizedBox(height: 8),
          _buildControlButton(
            heroTag: "clear",
            icon: Icons.clear,
            onPressed: widget.onClearTrace,
            backgroundColor: Colors.red,
          ),
          const SizedBox(height: 8),
          _buildControlButton(
            heroTag: "tracking",
            icon: widget.isTracking ? Icons.pause : Icons.play_arrow,
            onPressed: widget.onToggleTracking,
            backgroundColor: widget.isTracking ? Colors.orange : Colors.green,
          ),
          const SizedBox(height: 8),
          _buildControlButton(
            heroTag: "center",
            icon: Icons.gps_fixed,
            onPressed: _goToMyPosition,
            backgroundColor: Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required String heroTag,
    required IconData icon,
    required VoidCallback? onPressed,
    required Color backgroundColor,
  }) {
    return FloatingActionButton(
      heroTag: heroTag,
      onPressed: onPressed,
      backgroundColor: backgroundColor,
      mini: true,
      child: Icon(icon, color: Colors.white),
    );
  }

  String _formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.toStringAsFixed(0)} m';
    }
    return '${(distanceInMeters / 1000).toStringAsFixed(2)} km';
  }
}
