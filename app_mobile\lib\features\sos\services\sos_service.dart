import 'package:geolocator/geolocator.dart';
import 'api_service.dart';
import 'mqtt_service.dart';
import 'location_service.dart';
import 'voice_service.dart';

/// Types d'alertes SOS disponibles
enum SOSType {
  medical('<PERSON>e', 'Urgence médicale'),
  accident('Accident', 'Accident de circulation'),
  agression('Agression', 'Agression ou vol'),
  chute('Chute', 'Chute ou blessure'),
  malaise('Malaise', 'Malaise général'),
  autre('Autre', 'Autre urgence');

  const SOSType(this.apiValue, this.displayName);
  final String apiValue;
  final String displayName;
}

/// Niveaux d'urgence
enum UrgencyLevel {
  critique('Critique', 'Danger immédiat'),
  urgent('Urgent', 'Intervention rapide nécessaire'),
  modere('Modéré', 'Assistance requise');

  const UrgencyLevel(this.apiValue, this.displayName);
  final String apiValue;
  final String displayName;
}

/// Statut d'une alerte SOS
enum SOSStatus {
  pending('En attente'),
  sent('Envoyée'),
  acknowledged('Accusée de réception'),
  inProgress('En cours de traitement'),
  resolved('Résolue'),
  failed('Échec d\'envoi');

  const SOSStatus(this.displayName);
  final String displayName;
}

/// Modèle d'alerte SOS
class SOSAlert {
  final String id;
  final SOSType type;
  final String message;
  final UrgencyLevel urgencyLevel;
  final double latitude;
  final double longitude;
  final String locationName;
  final DateTime timestamp;
  final SOSStatus status;

  const SOSAlert({
    required this.id,
    required this.type,
    required this.message,
    required this.urgencyLevel,
    required this.latitude,
    required this.longitude,
    required this.locationName,
    required this.timestamp,
    required this.status,
  });

  SOSAlert copyWith({
    String? id,
    SOSType? type,
    String? message,
    UrgencyLevel? urgencyLevel,
    double? latitude,
    double? longitude,
    String? locationName,
    DateTime? timestamp,
    SOSStatus? status,
  }) {
    return SOSAlert(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      urgencyLevel: urgencyLevel ?? this.urgencyLevel,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
    );
  }
}

/// Service de gestion des alertes SOS
class SOSService {
  final ApiService _apiService = ApiService();
  final MqttService _mqttService = MqttService();
  final LocationService _locationService = LocationService();

  /// Déclenche une alerte SOS
  Future<SOSAlert> triggerSOS({
    required SOSType type,
    required String message,
    required UrgencyLevel urgencyLevel,
    Position? customPosition,
    String? videoPath,
    List<String>? photoPaths,
    String? audioPath,
    String? transcribedText,
  }) async {
    try {
      // 1. Obtenir la position
      Position position;
      if (customPosition != null) {
        position = customPosition;
      } else {
        position = await _locationService.getCurrentPosition() ??
            await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high,
            );
      }

      // 2. Obtenir le nom du lieu
      final locationName = await _locationService.getLocationName(
        position.latitude,
        position.longitude,
      );

      // 3. Enrichir le message avec les médias et le vocal
      String enrichedMessage = message;

      // Ajouter la transcription vocale si disponible
      if (transcribedText != null && transcribedText.isNotEmpty) {
        enrichedMessage += '\n🎤 Message vocal: "$transcribedText"';
      }

      if (videoPath != null) {
        enrichedMessage += '\n📹 Vidéo disponible: ${videoPath.split('/').last}';
      }
      if (photoPaths != null && photoPaths.isNotEmpty) {
        enrichedMessage += '\n📸 ${photoPaths.length} photo(s) disponible(s)';
      }
      if (audioPath != null) {
        enrichedMessage += '\n🔊 Audio disponible: ${audioPath.split('/').last}';
      }

      // 4. Créer l'alerte
      final alert = SOSAlert(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        message: enrichedMessage,
        urgencyLevel: urgencyLevel,
        latitude: position.latitude,
        longitude: position.longitude,
        locationName: locationName,
        timestamp: DateTime.now(),
        status: SOSStatus.pending,
      );

      // 5. Envoyer via API (priorité)
      try {
        await _apiService.addAlerte(
          typeAlerte: type.apiValue,
          message: enrichedMessage,
          status: true,
          niveauUrgence: urgencyLevel.apiValue,
          latitude: position.latitude,
          longitude: position.longitude,
        );

        // 6. Envoyer via MQTT (backup) avec informations médias
        _mqttService.publishSOSAlert(
          latitude: position.latitude,
          longitude: position.longitude,
        );

        return alert.copyWith(status: SOSStatus.sent);
      } catch (apiError) {
        print('Erreur API SOS: $apiError');
        
        // Fallback: essayer seulement MQTT
        try {
          _mqttService.publishSOSAlert(
            latitude: position.latitude,
            longitude: position.longitude,
          );
          return alert.copyWith(status: SOSStatus.sent);
        } catch (mqttError) {
          print('Erreur MQTT SOS: $mqttError');
          return alert.copyWith(status: SOSStatus.failed);
        }
      }
    } catch (e) {
      print('Erreur générale SOS: $e');
      rethrow;
    }
  }

  /// Récupère la liste des alertes depuis l'API
  Future<List<dynamic>> getAlertes() async {
    try {
      return await _apiService.fetchAlertes();
    } catch (e) {
      print('Erreur récupération alertes: $e');
      return [];
    }
  }

  /// Supprime une alerte
  Future<bool> deleteAlerte(int id) async {
    try {
      await _apiService.deleteAlerte(id);
      return true;
    } catch (e) {
      print('Erreur suppression alerte: $e');
      return false;
    }
  }

  /// Vérifie si les services sont disponibles
  Future<Map<String, bool>> checkServicesStatus() async {
    final results = <String, bool>{};

    // Test API
    try {
      await _apiService.fetchAlertes();
      results['api'] = true;
    } catch (e) {
      results['api'] = false;
    }

    // Test MQTT
    try {
      await _mqttService.connect();
      results['mqtt'] = true;
    } catch (e) {
      results['mqtt'] = false;
    }

    // Test GPS
    try {
      final position = await _locationService.getCurrentPosition();
      results['gps'] = position != null;
    } catch (e) {
      results['gps'] = false;
    }

    return results;
  }

  /// Formate un message SOS complet
  String formatSOSMessage(SOSAlert alert) {
    return '''
🆘 ALERTE SOS ${alert.urgencyLevel.apiValue.toUpperCase()}

📋 Type: ${alert.type.displayName}
💬 Message: ${alert.message}
📅 Heure: ${_formatDateTime(alert.timestamp)}
📍 Lieu: ${alert.locationName}
🌐 Coordonnées: ${alert.latitude.toStringAsFixed(6)}, ${alert.longitude.toStringAsFixed(6)}
🔗 Carte: https://www.google.com/maps/search/?api=1&query=${alert.latitude},${alert.longitude}

⚠️ Cette alerte nécessite une intervention ${alert.urgencyLevel.displayName.toLowerCase()}.
''';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
        '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.year} à '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
