import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../services/sos_service.dart';

/// Widget de bouton SOS rapide pour la homepage
class QuickSOSWidget extends StatefulWidget {
  final Position? currentPosition;
  final VoidCallback? onSOSTriggered;

  const QuickSOSWidget({
    super.key,
    this.currentPosition,
    this.onSOSTriggered,
  });

  @override
  State<QuickSOSWidget> createState() => _QuickSOSWidgetState();
}

class _QuickSOSWidgetState extends State<QuickSOSWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Urgence',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: GestureDetector(
                  onTap: _isLoading ? null : _showQuickSOSDialog,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: _isLoading ? Colors.grey : Colors.red,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 3,
                          )
                        : const Icon(
                            Icons.warning,
                            size: 40,
                            color: Colors.white,
                          ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 8),
          Text(
            _isLoading ? 'Envoi...' : 'SOS',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: _isLoading ? Colors.grey : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  void _showQuickSOSDialog() {
    showDialog(
      context: context,
      builder: (context) => SOSDialog(
        currentPosition: widget.currentPosition,
        onSOSTriggered: () {
          widget.onSOSTriggered?.call();
          Navigator.of(context).pop();
        },
        onLoadingChanged: (loading) {
          if (mounted) {
            setState(() {
              _isLoading = loading;
            });
          }
        },
      ),
    );
  }
}

/// Dialog de sélection du type de SOS
class SOSDialog extends StatefulWidget {
  final Position? currentPosition;
  final VoidCallback? onSOSTriggered;
  final Function(bool)? onLoadingChanged;

  const SOSDialog({
    super.key,
    this.currentPosition,
    this.onSOSTriggered,
    this.onLoadingChanged,
  });

  @override
  State<SOSDialog> createState() => _SOSDialogState();
}

class _SOSDialogState extends State<SOSDialog> {
  final SOSService _sosService = SOSService();
  SOSType _selectedType = SOSType.medical;
  UrgencyLevel _selectedUrgency = UrgencyLevel.critique;
  final TextEditingController _messageController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.warning, color: Colors.red, size: 28),
          SizedBox(width: 8),
          Text(
            'ALERTE SOS',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Type d\'urgence:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<SOSType>(
              value: _selectedType,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: SOSType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            const Text(
              'Niveau d\'urgence:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<UrgencyLevel>(
              value: _selectedUrgency,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: UrgencyLevel.values.map((level) {
                return DropdownMenuItem(
                  value: level,
                  child: Text(level.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedUrgency = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            const Text(
              'Message (optionnel):',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Décrivez la situation...',
                contentPadding: EdgeInsets.all(12),
              ),
              maxLines: 3,
              maxLength: 200,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _triggerSOS,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Text('ENVOYER SOS'),
        ),
      ],
    );
  }

  Future<void> _triggerSOS() async {
    setState(() {
      _isLoading = true;
    });
    widget.onLoadingChanged?.call(true);

    try {
      final message = _messageController.text.trim().isEmpty
          ? 'Alerte ${_selectedType.displayName.toLowerCase()}'
          : _messageController.text.trim();

      final alert = await _sosService.triggerSOS(
        type: _selectedType,
        message: message,
        urgencyLevel: _selectedUrgency,
        customPosition: widget.currentPosition,
      );

      if (mounted) {
        if (alert.status == SOSStatus.sent) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Alerte SOS envoyée avec succès !'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
          widget.onSOSTriggered?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ Alerte envoyée partiellement. Vérifiez votre connexion.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Échec de l\'envoi: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        widget.onLoadingChanged?.call(false);
      }
    }
  }
}
