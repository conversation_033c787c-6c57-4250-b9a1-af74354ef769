import 'package:canne_connectee/shared/models/call_record.dart';
import 'package:canne_connectee/shared/models/contact.dart';
import 'package:canne_connectee/shared/services/integrated_data_service.dart';
import 'package:flutter/material.dart';
import '../services/call_service.dart';

class AppelsPage extends StatefulWidget {
  const AppelsPage({super.key});

  @override
  State<AppelsPage> createState() => _AppelsPageState();
}

class _AppelsPageState extends State<AppelsPage> with SingleTickerProviderStateMixin {
  final IntegratedDataService _integratedService = IntegratedDataService();

  late TabController _tabController;
  bool _isLoading = true;
  bool _hasPermissions = false;

  List<Contact> _contacts = [];
  List<Contact> _emergencyContacts = [];
  List<CallRecord> _recentCalls = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _integratedService.initialize();

    // Vérifier les permissions
    final permissions = await _integratedService.getPermissionsStatus();
    _hasPermissions = permissions['phone'] == true;

    // Charger les données
    final allContacts = await _integratedService.getAllContacts();
    final allCalls = await _integratedService.getAllCallHistory();

    setState(() {
      _contacts = allContacts.where((c) =>
        c.lastCallDate != null || c.lastMessageDate != null
      ).take(10).toList();
      _emergencyContacts = allContacts.where((c) => c.isEmergency).toList();
      _recentCalls = allCalls.take(20).toList();
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        body: Center(
          child: CircularProgressIndicator(
            color: Color(0xFFFF7900),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text(
          'Appels',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_hasPermissions)
            IconButton(
              onPressed: () => _requestPermissions(),
              icon: const Icon(Icons.security),
              tooltip: 'Demander permissions',
            ),
          IconButton(
            onPressed: () => _showPermissionsDialog(),
            icon: const Icon(Icons.info_outline),
            tooltip: 'Statut permissions',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Urgence', icon: Icon(Icons.emergency, size: 20)),
            Tab(text: 'Contacts', icon: Icon(Icons.contacts, size: 20)),
            Tab(text: 'Historique', icon: Icon(Icons.history, size: 20)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEmergencyTab(),
          _buildContactsTab(),
          _buildHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildEmergencyTab() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message d'information
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'En cas d\'urgence réelle, utilisez ces numéros officiels',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange[700],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Numéros d'urgence officiels
          const Text(
            'Numéros d\'urgence',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView(
              children: _emergencyContacts.map((contact) {
                return _buildEmergencyContactItem(contact);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactsTab() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Barre de recherche
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const TextField(
              decoration: InputDecoration(
                hintText: 'Rechercher un contact...',
                border: InputBorder.none,
                icon: Icon(Icons.search, color: Color(0xFFFF7900)),
              ),
            ),
          ),

          const SizedBox(height: 20),

          const Text(
            'Contacts récents',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView(
              children: _contacts.map((contact) {
                return _buildContactItem(contact);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Historique des appels',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  _showClearHistoryDialog();
                },
                icon: const Icon(Icons.delete_outline, size: 18),
                label: const Text('Effacer'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: _recentCalls.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.call_outlined,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Aucun appel récent',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView(
                    children: _recentCalls.map((call) {
                      return _buildCallHistoryItem(call);
                    }).toList(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContactItem(Contact contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.red,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _makeEmergencyCall(contact),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getContactIcon(contact.type),
                    color: Colors.red,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        contact.phone,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 32,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactItem(Contact contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFF7900).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getContactIcon(contact.type),
              color: const Color(0xFFFF7900),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        contact.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    if (contact.isFavorite)
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  contact.formattedPhone,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  contact.lastActivityText,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _makeCall(contact),
            icon: const Icon(
              Icons.phone,
              color: Color(0xFFFF7900),
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCallHistoryItem(CallRecord call) {
    final contact = _integratedService.contactService.getContactById(call.contactId ?? '');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getCallTypeColor(call.type).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getCallTypeIcon(call.type),
              color: _getCallTypeColor(call.type),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        call.contactName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    if (call.isEmergency)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'URGENCE',
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  call.phoneNumber,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      call.timeText,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.grey,
                      ),
                    ),
                    if (call.formattedDuration.isNotEmpty) ...[
                      const Text(' • ', style: TextStyle(color: Colors.grey)),
                      Text(
                        call.formattedDuration,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _makeCallFromHistory(call),
            icon: const Icon(
              Icons.phone,
              color: Color(0xFFFF7900),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getContactIcon(ContactType type) {
    switch (type) {
      case ContactType.emergency:
        return Icons.emergency;
      case ContactType.medical:
        return Icons.local_hospital;
      case ContactType.family:
        return Icons.family_restroom;
      case ContactType.pharmacy:
        return Icons.local_pharmacy;
      case ContactType.transport:
        return Icons.local_taxi;
      case ContactType.service:
        return Icons.support_agent;
      default:
        return Icons.person;
    }
  }

  IconData _getCallTypeIcon(CallType type) {
    switch (type) {
      case CallType.incoming:
        return Icons.call_received;
      case CallType.outgoing:
        return Icons.call_made;
      case CallType.missed:
        return Icons.call_received;
      case CallType.rejected:
        return Icons.call_end;
    }
  }

  Color _getCallTypeColor(CallType type) {
    switch (type) {
      case CallType.incoming:
        return Colors.green;
      case CallType.outgoing:
        return Colors.blue;
      case CallType.missed:
        return Colors.red;
      case CallType.rejected:
        return Colors.orange;
    }
  }

  Future<void> _makeCall(Contact contact) async {
    final success = await _integratedService.callService.makeCall(
      contact.phone,
      contactId: contact.id,
      contactName: contact.name,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Appel vers ${contact.name}...'),
          backgroundColor: const Color(0xFFFF7900),
          duration: const Duration(seconds: 2),
        ),
      );

      // Actualiser les données
      _initializeServices();
    }
  }

  Future<void> _makeEmergencyCall(Contact contact) async {
    final success = await _integratedService.callService.makeEmergencyCall(
      contact.phone,
      contact.name,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Appel d\'urgence vers ${contact.name}...'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );

      // Actualiser les données
      _initializeServices();
    }
  }

  Future<void> _makeCallFromHistory(CallRecord call) async {
    final success = await _integratedService.callService.makeCall(
      call.phoneNumber,
      contactId: call.contactId,
      contactName: call.contactName,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Appel vers ${call.contactName}...'),
          backgroundColor: const Color(0xFFFF7900),
          duration: const Duration(seconds: 2),
        ),
      );

      // Actualiser les données
      _initializeServices();
    }
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer l\'historique'),
        content: const Text(
          'Voulez-vous vraiment effacer tout l\'historique des appels locaux ?\n\nNote: L\'historique de l\'appareil ne sera pas affecté.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _integratedService.callService.clearCallHistory();
              _initializeServices();

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Historique local effacé'),
                    backgroundColor: Color(0xFFFF7900),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Effacer'),
          ),
        ],
      ),
    );
  }

  Future<void> _requestPermissions() async {
    final success = await _integratedService.requestPermissions();

    if (success) {
      setState(() {
        _hasPermissions = true;
      });

      // Recharger les données avec les nouvelles permissions
      _initializeServices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions accordées ! Accès aux données de l\'appareil activé.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions refusées. Fonctionnalités limitées.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showPermissionsDialog() async {
    final permissions = await _integratedService.getPermissionsStatus();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.blue),
            SizedBox(width: 8),
            Text('Statut des permissions'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Permissions nécessaires pour accéder aux données de l\'appareil:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            _buildPermissionItem('Téléphone', permissions['phone'] ?? false),
            _buildPermissionItem('SMS', permissions['sms'] ?? false),
            _buildPermissionItem('Contacts', permissions['contacts'] ?? false),
            const SizedBox(height: 16),
            Text(
              _integratedService.isDeviceAccessEnabled
                  ? '✅ Accès aux données de l\'appareil activé'
                  : '❌ Accès aux données de l\'appareil désactivé',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: _integratedService.isDeviceAccessEnabled
                    ? Colors.green
                    : Colors.red,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (!_integratedService.isDeviceAccessEnabled)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _requestPermissions();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Demander'),
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(String name, bool granted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.cancel,
            color: granted ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(name),
        ],
      ),
    );
  }

  void _showAddContactDialog(BuildContext context) {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    ContactType selectedType = ContactType.personal;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Ajouter un contact'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nom',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Numéro de téléphone',
                      border: OutlineInputBorder(),
                      prefixText: '+225 ',
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<ContactType>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Type de contact',
                      border: OutlineInputBorder(),
                    ),
                    items: ContactType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedType = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (nameController.text.isNotEmpty &&
                        phoneController.text.isNotEmpty) {
                      final now = DateTime.now();
                      final contact = Contact(
                        id: '',
                        name: nameController.text,
                        phone: '+225 ${phoneController.text}',
                        type: selectedType,
                        createdAt: now,
                        updatedAt: now,
                      );

                      await _integratedService.contactService.addContact(contact);
                      Navigator.of(context).pop();
                      _initializeServices();

                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Contact ${contact.name} ajouté'),
                            backgroundColor: const Color(0xFFFF7900),
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF7900),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Ajouter'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
